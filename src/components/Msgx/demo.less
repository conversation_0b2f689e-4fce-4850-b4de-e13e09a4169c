@import "@arco-design/mobile-react/style/mixin.less";

.msgx-demo {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .demo-header {
    text-align: center;
    margin-bottom: 30px;

    .demo-title {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: #1d2129;
      margin-bottom: 8px;
    }

    .demo-subtitle {
      display: block;
      font-size: 14px;
      color: #86909c;
    }
  }

  .demo-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
      border-bottom: 1px solid #e5e6eb;
      padding-bottom: 8px;
    }

    .direction-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      align-items: center;
      justify-items: center;
    }

    .custom-examples {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }

    .demo-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      position: relative;

      .demo-button {
        min-width: 80px;
        font-size: 12px;
      }
    }
  }

  .demo-footer {
    text-align: center;
    padding: 20px;

    .footer-text {
      font-size: 14px;
      color: #86909c;
    }
  }
}

// 暗色模式适配
.use-dark-mode-query({
  .msgx-demo {
    background-color: #17171a;

    .demo-header {
      .demo-title {
        color: #f6f6f6;
      }

      .demo-subtitle {
        color: #929293;
      }
    }

    .demo-section {
      background: #232324;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

      .section-title {
        color: #f6f6f6;
        border-bottom-color: #484849;
      }
    }

    .demo-footer {
      .footer-text {
        color: #929293;
      }
    }
  }
});

// 响应式适配
@media (max-width: 768px) {
  .msgx-demo {
    padding: 15px;

    .demo-section {
      padding: 15px;

      .direction-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
      }

      .custom-examples {
        gap: 15px;
      }

      .demo-item {
        padding: 15px;

        .demo-button {
          min-width: 70px;
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .msgx-demo {
    .demo-section {
      .direction-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .custom-examples {
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
