import React, { forwardRef, useImperativeHandle } from 'react';
import { View } from '@tarojs/components';
import './index.less';

export type MsgxDirection =
  | 'topLeft'
  | 'topCenter'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomCenter'
  | 'bottomRight'
  | 'leftTop'
  | 'leftCenter'
  | 'leftBottom'
  | 'rightTop'
  | 'rightCenter'
  | 'rightBottom';

export interface MsgxProps {
  /**
   * 消息框展示的位置
   * @default 'topCenter'
   */
  direction?: MsgxDirection;
  /**
   * 消息框内容
   */
  children: React.ReactNode;
  /**
   * 是否显示消息框
   * @default true
   */
  visible?: boolean;
  /**
   * 消息框垂直方向上相对于子元素的偏移量，单位为px
   * @default 10
   */
  verticalOffset?: number;
  /**
   * 消息框水平方向上相对于子元素的偏移量，单位为px
   * @default 10
   */
  horizontalOffset?: number;
  /**
   * 箭头大小，单位为px
   * @default 8
   */
  arrowSize?: number;
  /**
   * 消息框背景色
   * @default '#000'
   */
  backgroundColor?: string;
  /**
   * 消息框文字颜色
   * @default '#fff'
   */
  textColor?: string;
  /**
   * 消息框圆角大小
   * @default 4
   */
  borderRadius?: number;
  /**
   * 消息框内边距
   * @default '8px 12px'
   */
  padding?: string;
  /**
   * 消息框最小宽度
   * @default 'auto'
   */
  minWidth?: string;
  /**
   * 消息框最大宽度
   * @default '90vw'
   */
  maxWidth?: string;
  /**
   * 消息框阴影
   * @default '0 2px 8px rgba(0, 0, 0, 0.15)'
   */
  boxShadow?: string;
  /**
   * 消息框z-index
   * @default 1000
   */
  zIndex?: number;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 消息框内容，如果提供则显示在气泡中，children作为触发元素
   */
  content?: React.ReactNode;
}

export interface MsgxRef {
  /**
   * 手动更新位置
   */
  updatePosition: () => void;
}

const Msgx = forwardRef<MsgxRef, MsgxProps>((props, ref) => {
  const {
    direction = 'topCenter',
    children,
    content,
    visible = true,
    verticalOffset = 10,
    horizontalOffset = 10,
    arrowSize = 8,
    backgroundColor = '#000',
    textColor = '#fff',
    borderRadius = 4,
    padding = '8px 12px',
    minWidth = 'auto',
    maxWidth = '90vw',
    boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)',
    zIndex = 1000,
    className = '',
    style = {},
  } = props;

  useImperativeHandle(ref, () => ({
    updatePosition: () => {
      // 在实际项目中可以实现位置更新逻辑
      console.log('updatePosition called');
    },
  }));

  // 获取容器样式
  const getContainerStyle = (): React.CSSProperties => {
    return {
      position: 'relative',
      display: 'inline-block',
      ...style,
    };
  };

  // 获取内容样式
  const getContentStyle = (): React.CSSProperties => {
    const contentStyle: React.CSSProperties = {
      backgroundColor,
      color: textColor,
      borderRadius: `${borderRadius}px`,
      padding,
      minWidth,
      maxWidth,
      boxShadow,
      zIndex,
      fontSize: '14px',
      lineHeight: '1.4',
    };

    return contentStyle;
  };

  // 获取箭头样式
  const getArrowStyle = (): React.CSSProperties => {
    return {
      borderWidth: `${arrowSize}px`,
    };
  };

  // 获取定位样式变量
  const getPositionVars = () => {
    return {
      '--msgx-vertical-offset': `${verticalOffset}px`,
      '--msgx-horizontal-offset': `${horizontalOffset}px`,
      '--msgx-arrow-size': `${arrowSize}px`,
      '--msgx-bg-color': backgroundColor,
    } as React.CSSProperties;
  };

  return (
    <View
      className={`msgx-container ${className}`}
      style={{
        ...getContainerStyle(),
        ...getPositionVars(),
      }}
    >
      {children}
      {visible && (content || children) && (
        <View
          className={`msgx-content msgx-${direction}`}
          style={getContentStyle()}
        >
          <View
            className={`msgx-arrow msgx-arrow-${direction}`}
            style={getArrowStyle()}
          />
          <View className="msgx-inner">
            {content || children}
          </View>
        </View>
      )}
    </View>
  );
});

Msgx.displayName = 'Msgx';

export default Msgx;
