import React, { useState, useEffect, useRef, useMemo } from 'react';
import { View, Text, Canvas } from '@tarojs/components';
import { Button, Image, ImagePreview, Avatar } from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import Taro from '@tarojs/taro';
import { QRCodeSVG } from 'qrcode.react';
import html2canvas from 'html2canvas';

import {URL_BASE} from '@/utils/api/urls';
import logoImg from '@/assets/images/common/logo.png';
import defaultHeadImg from '@/assets/images/common/default_head.png';
import wechatIcon from '@/assets/images/common/wx_icon.png';
import { IconWechat } from '@/components/YkIcons';
import BottomPopup from '@/components/BottomPopup';
import NativeBridge from '@/utils/nativeBridge';
import './index.less';

// 开发环境下导入测试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/webviewTest').then(({ WebViewTester }) => {
    // 暴露测试工具到全局，方便控制台调试
    (window as any).WebViewTester = WebViewTester;
    console.log('🛠️ WebView测试工具已加载，在控制台输入 WebViewTester.runAllTests() 运行测试');
  });
}

// 声明全局常量
declare const APP_NAME_CN: string;

const QrcodePage: React.FC = () => {
  const userInfo = Taro.getStorageSync('userInfo');
  const canvasRef = useRef<any>(null);
  const [qrcodeImage, setQrcodeImage] = useState<string>('');
  const [isActionPopupVisible, setIsActionPopupVisible] = useState<boolean>(false);

  // 使用 useMemo 缓存二维码URL，只有当用户ID或baseUrl变化时才重新计算
  const qrcodeUrl = useMemo(() => {
    if (!userInfo?.id) return '';
    
    const baseUrl = Taro.getStorageSync('baseUrl') || URL_BASE;
    return `${baseUrl}/#/pages/followUser/index?uid=${userInfo.id}&redirect=wx`;
  }, [userInfo?.id]);

  // 当二维码URL变化时，清理缓存的图片
  useEffect(() => {
    if (qrcodeUrl && qrcodeImage) {
      setQrcodeImage(''); // 清理旧的图片缓存
    }
  }, [qrcodeUrl]);

  // 使用 useMemo 优化图片转换逻辑，只有当二维码URL变化时才重新生成图片
  const generateQrcodeImage = useMemo(() => {
    return () => {
      if (!qrcodeUrl) return Promise.reject('二维码URL未生成');
      
      return new Promise<string>((resolve, reject) => {
        if (typeof window !== 'undefined' && typeof document !== 'undefined') {
          const element = document.getElementById('qrcode-page-content');
          if (!element) {
            reject('找不到二维码容器元素');
            return;
          }
          
          html2canvas(element, {
            useCORS: true,
            scale: 2,
            logging: false
          }).then(canvas => {
            // 生成 JPEG 格式的 Base64 数据，与 Android 端保持一致
            const base64Image = canvas.toDataURL('image/jpeg', 0.9); // 0.9 质量
            resolve(base64Image);
          }).catch(error => {
            reject(error);
          });
        } else {
          reject('当前环境不支持此功能');
        }
      });
    };
  }, [qrcodeUrl]);

  // 将二维码元素转换为图片并打开预览
  const convertToImage = async () => {
    // 如果图片已经生成且二维码URL没有变化，直接打开预览
    if (qrcodeImage && qrcodeUrl) {
      openImagePreview(qrcodeImage);
      return;
    }

    // Taro.showLoading({ title: '生成图片中...' });
    
    try {
      const base64Image = await generateQrcodeImage();
      setQrcodeImage(base64Image);
      Taro.hideLoading();
      openImagePreview(base64Image);
    } catch (error) {
      console.error('转换图片失败', error);
      Taro.hideLoading();
      Taro.showToast({
        title: '生成图片失败',
        icon: 'none'
      });
    }
  };

  // 使用 ImagePreview 打开图片预览
  const openImagePreview = (imageSrc: string) => {
    ImagePreview.open({
      images: [{ src: imageSrc }],
      openIndex: 0,
      onImageLongTap: (index, image, e) => {
        // 长按图片时显示操作菜单
        e.preventDefault();
        if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
          // H5环境下长按图片时提示保存方法
          Taro.showActionSheet({
            itemList: ['保存图片'],
            success: function (res) {
              if (res.tapIndex === 0) {
                saveImageToAlbum();
              }
            }
          });
        } else {
          setIsActionPopupVisible(true);
        }
      }
    });
  };

  // 点击二维码区域
  const handleQrcodeClick = () => {
    convertToImage();
  };



  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack();
  };

  // 分享
  const handleShare = async () => {
    try {
      // 如果图片还没有生成，先生成图片
      if (!qrcodeImage) {
        await convertToImage();
        // 等待状态更新后再显示操作菜单
        setTimeout(() => {
          setIsActionPopupVisible(true);
        }, 100);
      } else {
        // 如果图片已经存在，直接显示操作菜单
        setIsActionPopupVisible(true);
      }
    } catch (error) {
      console.error('分享准备失败', error);
    }
  };

  // 处理底部弹出菜单操作
  const handleActionConfirm = async (index: number) => {
    switch (index) {
      case 0: // 转发到微信好友
        await handleShareToWechat();
        break;
      case 1: // 保存图片
        await handleSaveImage();
        break;
      default:
        break;
    }
  };

  // 处理分享到微信好友
  const handleShareToWechat = async () => {
    try {
      // 检测环境并处理分享
      if (NativeBridge.isInWebView()) {
        // WebView环境 - 使用原生分享
        // 确保图片已生成
        let imageData = qrcodeImage;
        if (!imageData) {
          Taro.showLoading({ title: '准备分享内容...' });
          try {
            imageData = await generateQrcodeImage();
            setQrcodeImage(imageData);
          } catch (error) {
            Taro.hideLoading();
            Taro.showToast({
              title: '生成分享图片失败',
              icon: 'none'
            });
            return;
          }
          Taro.hideLoading();
        }

        // 使用新的分享方法分享Base64图片到微信好友
        try {
          NativeBridge.shareBase64ImageToWechat(imageData);
          Taro.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 1500
          });
        } catch (error) {
          console.error('分享失败:', error);
          Taro.showToast({
            title: '分享失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      } else if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
        // 纯H5环境 - 提示用户使用浏览器分享
        if (navigator.share && qrcodeImage) {
          // 支持Web Share API
          try {
            // 将base64转换为Blob
            const response = await fetch(qrcodeImage);
            const blob = await response.blob();
            const file = new File([blob], 'qrcode.png', { type: 'image/png' });
            
            await navigator.share({
              title: '我的相册共享二维码',
              text: '扫码可免费一键转图',
              files: [file]
            });
          } catch (error) {
            console.error('Web分享失败:', error);
            // 降级处理
            showFallbackShareOptions();
          }
        } else {
          // 不支持Web Share API，显示备选方案
          showFallbackShareOptions();
        }
      } else {
        // 小程序环境
        Taro.showShareMenu({
          withShareTicket: true
        });
        Taro.showToast({
          title: '请点击右上角分享',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('分享处理失败:', error);
      Taro.showToast({
        title: '分享失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  };

  // 处理保存图片
  const handleSaveImage = async () => {
    try {
      // 确保图片已生成
      let imageData = qrcodeImage;
      if (!imageData) {
        Taro.showLoading({ title: '生成图片中...' });
        try {
          imageData = await generateQrcodeImage();
          setQrcodeImage(imageData);
        } catch (error) {
          Taro.hideLoading();
          Taro.showToast({
            title: '生成图片失败',
            icon: 'none'
          });
          return;
        }
        Taro.hideLoading();
      }

      if (NativeBridge.isInWebView()) {
        // WebView环境 - 直接使用原生下载方法
        try {
          // 验证数据
          if (!imageData || imageData.length < 100) {
            throw new Error('图片数据无效');
          }
          
          // 格式转换：确保与 Android 端期望的格式一致
          let processedImageData = imageData;
          if (imageData.startsWith('data:image/png;base64,')) {
            // 将 PNG 格式转换为 JPEG 格式（Android 端期望的格式）
            processedImageData = imageData.replace('data:image/png;base64,', 'data:image/jpeg;base64,');
            console.log('🔄 已将 PNG 格式转换为 JPEG 格式');
          }
          
          // 添加调试信息
          console.log('📊 保存图片数据信息:');
          console.log('- 原始数据长度:', imageData.length);
          console.log('- 处理后数据长度:', processedImageData.length);
          console.log('- 原始数据前缀:', imageData.substring(0, 30) + '...');
          console.log('- 处理后数据前缀:', processedImageData.substring(0, 30) + '...');
          console.log('- 是否包含原生接口:', !!window.downloadBase64Img);
          
          Taro.showLoading({ title: '正在保存...' });
          
          // 直接传递处理后的 Base64 数据给原生方法
          if (window.downloadBase64Img) {
            console.log('🔧 调用原生保存方法...');
            window.downloadBase64Img.downloadBase64Img(processedImageData);
            console.log('✅ 原生方法调用完成');
          } else {
            throw new Error('原生下载接口不可用');
          }
          
          // 显示成功提示（因为原生方法是异步的，这里直接提示）
          setTimeout(() => {
            Taro.hideLoading();
            Taro.showToast({
              title: '保存请求已发送',
              icon: 'success',
              duration: 2000
            });
          }, 1000);
          
        } catch (error) {
          Taro.hideLoading();
          console.error('❌ 保存图片失败:', error);
          console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            imageDataLength: imageData?.length || 0,
            hasNativeInterface: !!window.downloadBase64Img
          });
          
          // 降级处理：提示用户手动保存
          Taro.showModal({
            title: '保存失败',
            content: '图片保存失败，建议您截图保存。\n\n错误信息：' + (error.message || '未知错误'),
            confirmText: '知道了',
            showCancel: false
          });
        }
      } else {
        // H5或小程序环境 - 使用原有逻辑
        saveImageToAlbum();
      }
    } catch (error) {
      console.error('保存图片失败:', error);
      Taro.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  };

  // 显示备选分享方案
  const showFallbackShareOptions = () => {
    Taro.showModal({
      title: '分享选项',
      content: '您可以长按二维码图片保存到相册，然后通过微信发送给好友',
      confirmText: '打开微信',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 尝试打开微信（如果支持的话）
          window.location.href = 'weixin://';
        }
      }
    });
  };

  // 保存图片到相册
  const saveImageToAlbum = () => {
    if (!qrcodeImage) {
      Taro.showToast({
        title: '图片未生成',
        icon: 'none'
      });
      return;
    }

    Taro.showLoading({ title: '正在保存...' });

    // 区分环境：H5环境和小程序环境
    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      // H5环境下使用a标签下载
      try {
        const link = document.createElement('a');
        link.download = `qrcode_${Date.now()}.png`;
        link.href = qrcodeImage;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        Taro.hideLoading();
        Taro.showToast({
          title: '图片已准备，请确认下载',
          icon: 'none',
          duration: 2000
        });
      } catch (error) {
        console.error('保存图片失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败，请长按图片手动保存',
          icon: 'none',
          duration: 2000
        });
      }
    } else {
      // 小程序环境
      try {
        const base64Data = qrcodeImage.split(',')[1];
        const fsm = Taro.getFileSystemManager();
        const filePath = `${Taro.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;
        
        fsm.writeFile({
          filePath,
          data: base64Data,
          encoding: 'base64',
          success: () => {
            Taro.saveImageToPhotosAlbum({
              filePath: filePath,
              success: () => {
                Taro.hideLoading();
                Taro.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: (err) => {
                console.error('保存到相册失败', err);
                Taro.hideLoading();
                if (err.errMsg.indexOf('auth deny') >= 0) {
                  Taro.showModal({
                    title: '提示',
                    content: '保存图片需要您授权相册权限',
                    confirmText: '去授权',
                    success: (res) => {
                      if (res.confirm) {
                        Taro.openSetting();
                      }
                    }
                  });
                } else {
                  Taro.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              }
            });
          },
          fail: (err) => {
            console.error('写入文件失败', err);
            Taro.hideLoading();
            Taro.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('写入文件失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  };

  return (
    <View className="qrcode-page">
      {/* 导航栏 */}
      <YkNavBar title="二维码" backArrow onClickLeft={handleBack} />
      
      <View className="qrcode-page-content" id="qrcode-page-content">
        <View className="qrcode-container">
          {/* 头部区域 - 匹配DSL容器12 */}
          <View className="qrcode-header">
            {/* LOGO区域 - 匹配DSL LOGO样式 */}
            <View className="qrcode-logo">
              {/* <Image src={logoImg} style={{ width: 34, height: 35 }} /> */}
              <Image className="qrcode-logo-img" src={logoImg} fit="contain"/>
            </View>
            
            {/* 标题容器 - 匹配DSL容器615 */}
            <View className="qrcode-title-container">
              <Text className="qrcode-title">我的相册共享二维码</Text>
              <Text className="qrcode-subtitle">扫码可免费一键转图</Text>
            </View>
          </View>

          {/* 二维码内容区域 - 匹配DSL容器16 */}
          <View className="qrcode-content">
            {/* 用户信息区域 - 匹配DSL容器13 */}
            <View className="user-info">
              <Image 
                className="user-avatar" 
                src={userInfo.avatar || defaultHeadImg} 
              />
              <Text className="user-name">{userInfo.nickname || APP_NAME_CN}</Text>
            </View>
            
            {/* 二维码框 - 匹配DSL二维码区域 */}
            <View className="qrcode-box" onClick={handleQrcodeClick}>
              {qrcodeUrl ? (
                <QRCodeSVG
                  value={qrcodeUrl}
                  size={156} // 调整为适合160x160容器的大小
                  bgColor="#ffffff"
                  fgColor="#000000"
                  level="H"
                />
              ) : (
                <Canvas 
                  className="qrcode-canvas" 
                  canvasId="qrcode-canvas" 
                  ref={canvasRef}
                />
              )}
            </View>

            {/* 底部提示区域 - 匹配DSL容器14 */}
            <View className="qrcode-tip">
              <IconWechat className="wechat-icon" />
              {/* <Image className="wechat-icon" src={wechatIcon} /> */}
              <Text className="tip-text">扫码共享我的相册</Text>
            </View>
          </View>
        </View>
      </View>

      {/* 底部分享按钮 */}
      <View className="footer-button">
        <Button type="primary" className="share-button" onClick={handleShare}>
          分享
        </Button>
      </View>

      {/* 底部操作弹窗 */}
      <BottomPopup
        options={["转发到微信好友", "保存图片"]}
        btnCloseText="取消"
        onConfirm={handleActionConfirm}
        onClose={() => setIsActionPopupVisible(false)}
        visible={isActionPopupVisible}
      />
    </View>
  );
};

export default QrcodePage; 