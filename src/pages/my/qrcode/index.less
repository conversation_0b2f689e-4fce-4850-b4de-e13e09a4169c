@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

// 直接使用Arco Design和项目预定义的设计token变量
// 基于DSL映射关系，无需重新定义变量

// DSL paint_3:9718 - Logo绿色 (DSL特定品牌色)
@qr-logo-green: #6CBE70;          
// DSL paint_3:07600 - 微信绿色 (DSL特定功能色)
@qr-wechat-green: #54B736;        
// DSL paint_3:07811 - 图片预览蒙层背景 (DSL特定样式)
@qr-preview-mask-bg: rgba(0, 0, 0, 0.9); 
// DSL effect_3:00287 - popover阴影 (DSL特定效果)
@qr-popover-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

.qrcode-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: @container-background-color;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @--text-5; // 使用variables.less中的深色模式颜色
  });
}

// 页面内容区域 - 匹配DSL结构 (375x630)
.qrcode-page-content {
  flex: 1;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 375px;
  height: 630px;
  margin: 0 auto;
  overflow: hidden;
  background-color: @container-background-color;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @--text-5; // 使用variables.less中的深色模式颜色
  });
}

// 主容器 - 匹配DSL容器616 (220x499)
.qrcode-container {
  width: 220px;
  height: 499px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 60px; // 匹配DSL的gap配置
  cursor: pointer;
  &:active {
    opacity: 0.9;
  }
}

// 头部区域 - 匹配DSL容器12 (220x128)
.qrcode-header {
  width: 220px;
  height: 128px;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; // 匹配DSL的gap配置
}

// Logo容器 - 匹配DSL LOGO样式 (70x70)
.qrcode-logo {
  width: 70px;
  height: 70px;
  background: @container-background-color;
  box-shadow: @qr-popover-shadow;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  &-img {
    width: 100%;
    height: 100%;
  }
}

// 标题容器 - 匹配DSL容器615 (135x38)
.qrcode-title-container {
  width: 135px;
  height: 38px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qrcode-title {
  width: 135px;
  height: 21px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: ~`pxtorem(15)`;
  font-weight: bold;
  color: @font-color;
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: @--text-1; // 使用variables.less中的浅色文字
  });
  line-height: ~`pxtorem(21)`;
  text-align: center;
  white-space: nowrap;
}

.qrcode-subtitle {
  width: 108px;
  height: 17px;
  font-family: 'PingFangSC-Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: ~`pxtorem(12)`;
  color: @sub-info-font-color;
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: @--text-2; // 使用variables.less中的次要文字颜色
  });
  line-height: ~`pxtorem(17)`;
  text-align: center;
}

// 二维码内容区域 - 匹配DSL容器16 (220x311)
.qrcode-content {
  width: 220px;
  height: 311px;
  background: @container-background-color;
  box-shadow: @qr-popover-shadow;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 22px; // 匹配DSL的gap配置
  padding: 15px 0px 8px; // 匹配DSL的padding配置
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.qrcode-card {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

// 用户信息区域 - 匹配DSL容器13 (220x64)
.user-info {
  width: 220px;
  height: 64px;
  flex-shrink: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px; // 匹配DSL的gap配置
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.user-name {
  width: 100px;
  height: 21px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: ~`pxtorem(15)`;
  font-weight: bold;
  color: @font-color;
  .use-var(color, font-color);
  .use-dark-mode-query({
    color: @--text-1; // 使用variables.less中的浅色文字
  });
  line-height: ~`pxtorem(21)`;
  text-align: center;
}

// 二维码框 - 匹配DSL二维码区域 (160x160)
.qrcode-box {
  width: 160px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0.5px dashed @line-color;
  border-radius: 2px;
  .use-var(border-color, line-color);
  .use-dark-mode-query({
    border-color: @dark-line-color;
  });
  padding: 0;
  background-color: @container-background-color;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @--text-5; // 使用variables.less中的深色背景色
  });
}

// .qrcode-svg-container {
  
// }

.qrcode-canvas {
  width: 160px;
  height: 160px;
  display: block;
  margin: auto;
}

// 底部提示区域 - 匹配DSL容器14 (220x20)
.qrcode-tip {
  width: 220px;
  height: 20px;
  flex-shrink: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px; // 匹配DSL的gap配置
}

.wechat-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.tip-text {
  width: 96px;
  height: 17px;
  font-family: 'PingFangSC-Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: ~`pxtorem(12)`;
  color: @sub-info-font-color;
  .use-var(color, sub-info-font-color);
  .use-dark-mode-query({
    color: @--text-2; // 使用variables.less中的次要文字颜色
  });
  line-height: ~`pxtorem(17)`;
  text-align: center;
  white-space: nowrap;
}

.footer-button {
  position: absolute;
  bottom: 34px;
  left: 0;
  right: 0;
  width: 100%;
  padding: 16px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  
  .share-button {
    width: 164px;
    height: 32px;
    padding: 16px 16px;
    background-color: #1677ff;
    .use-var(background-color, primary-color);
    .use-dark-mode-query({
      background-color: @dark-primary-color;
    });
    border-radius: 2px;
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 图片预览样式已移除，现在使用 Arco Design 的 ImagePreview 组件 