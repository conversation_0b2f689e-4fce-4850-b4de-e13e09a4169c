// AI聊天页面类型定义

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image'
}

// 消息发送者类型
export enum SenderType {
  USER = 'user',
  AI = 'assistant'
}

// 消息接口
export interface ChatMessage {
  id: string;
  type: MessageType;
  content: string;
  sender: SenderType;
  timestamp: number;
  avatar?: string;
  loading?: boolean; // 用于显示AI回复的加载状态
}

// 对话接口
export interface Conversation {
  id: string;
  title: string;
  createTime: string;
  updateTime: string;
  pinned?: boolean;
}

// 页面状态接口
export interface ChatPageState {
  loading: boolean;
  conversationId: string | null;
  messages: ChatMessage[];
  inputText: string;
  keyboardHeight: number;
  sending: boolean;
  initialized: boolean;
}

// 用户信息接口
export interface UserInfo {
  user_id: string;
  nickName: string;
  head_img?: string;
}

// SSE消息数据接口
export interface SSEMessageData {
  code: number;
  msg: string;
  data: {
    send?: {
      id: number;
      type: string;
      content: string;
      createTime: number;
    };
    receive?: {
      id: number;
      type: string;
      content: string;
      createTime: number;
    };
  };
}
