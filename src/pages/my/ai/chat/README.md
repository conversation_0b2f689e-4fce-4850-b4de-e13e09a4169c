# AI客服聊天页面

## 功能概述

这是一个完整的AI客服聊天页面，支持与AI进行实时对话，包含以下主要功能：

### 核心功能
- ✅ AI对话管理（获取/创建对话）
- ✅ 消息发送与接收
- ✅ SSE流式消息支持
- ✅ 消息列表展示
- ✅ 键盘高度自适应
- ✅ 消息自动滚动
- ✅ 加载状态显示
- ✅ 错误处理

### 技术特性
- ✅ TypeScript类型安全
- ✅ 响应式设计
- ✅ 暗黑模式支持
- ✅ 调试系统集成
- ✅ 组件化架构

## 文件结构

```
src/pages/my/ai/chat/
├── index.tsx           # 主组件
├── index.less          # 样式文件
├── index.config.ts     # 页面配置
├── types.ts            # 类型定义
└── README.md           # 说明文档
```

## 使用的组件

### Arco Design 组件
- `Textarea` - 消息输入框
- `Button` - 发送按钮和调试按钮
- `Avatar` - 用户和AI头像
- `Image` - 图片消息（预留）
- `ImagePreview` - 图片预览（预留）
- `Loading` - 加载状态

### 自定义组件
- `YkNavBar` - 导航栏
- `Msgx` - 消息提示组件

### Taro组件
- `View` - 容器组件
- `Text` - 文本组件
- `ScrollView` - 滚动容器

## API接口

### 对话管理
- `getAiChatConversationList()` - 获取对话列表
- `createAiChatConversation()` - 创建新对话
- `getAiChatMessagePage()` - 获取消息分页
- `sendAiChatMessageStream()` - 发送流式消息

## 调试功能

页面集成了完整的调试系统，支持URL参数控制：

### 调试模式激活
在URL中添加 `debug` 参数即可激活调试模式：
- `?debug` - 基础调试模式
- `?debug=mock` - 模拟数据模式
- `?debug=api` - API调用调试
- `?debug=loading` - 加载状态测试
- `?debug=error` - 错误处理测试

### 调试功能
- 添加测试消息
- 添加AI测试消息
- 清空消息列表
- 模拟键盘弹起/收起
- 测试加载状态
- 手动滚动到底部

## 样式特性

### 响应式设计
- 支持不同屏幕尺寸
- 键盘弹起时自动调整布局
- 消息气泡自适应宽度

### 暗黑模式
- 完整的暗黑模式支持
- 使用Arco Design色彩系统
- 动态主题切换

### 消息样式
- 用户消息（右侧，蓝色气泡）
- AI消息（左侧，白色气泡带边框）
- 消息时间显示
- 头像展示
- 加载状态动画

## 键盘适配

### 多平台支持
- Taro原生键盘监听
- H5环境视觉视口监听
- 自动滚动到最新消息

### 适配策略
- 输入框固定在底部
- 键盘弹起时动态调整位置
- 消息列表自动滚动

## 错误处理

### 网络错误
- API调用失败降级处理
- SSE连接失败模拟回复
- 用户友好的错误提示

### 调试模式
- 详细的错误日志
- 错误场景模拟
- 降级方案测试

## 性能优化

### 消息渲染
- 虚拟化长列表（可扩展）
- 消息时间智能显示
- 图片懒加载（预留）

### 内存管理
- 组件卸载时清理监听器
- SSE连接自动关闭
- 防止内存泄漏

## 扩展功能

### 已预留接口
- 图片消息支持
- 文件上传功能
- 表情包支持
- 消息撤回
- 历史消息加载

### 可扩展特性
- 多媒体消息
- 语音输入
- 消息搜索
- 对话导出

## 使用示例

### 基础使用
```typescript
// 页面跳转
Taro.navigateTo({
  url: '/pages/my/ai/chat/index'
});
```

### 调试模式
```typescript
// 激活调试模式
Taro.navigateTo({
  url: '/pages/my/ai/chat/index?debug=mock'
});
```

## 注意事项

1. **用户信息依赖**：页面依赖本地存储的用户信息，确保在跳转前已登录
2. **网络环境**：SSE功能需要稳定的网络连接
3. **权限配置**：确保API接口权限配置正确
4. **调试模式**：生产环境建议关闭调试功能

## 更新日志

### v1.0.0 (2024-01-12)
- ✅ 完成基础聊天功能
- ✅ 集成AI对话接口
- ✅ 实现SSE流式消息
- ✅ 添加键盘适配
- ✅ 集成调试系统
- ✅ 支持暗黑模式
- ✅ 完善错误处理
