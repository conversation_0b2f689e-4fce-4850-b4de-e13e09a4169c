import { View, Text, ScrollView } from '@tarojs/components';
import { useState, useEffect, useRef } from 'react';
import { Textarea, Button, Avatar, Image, ImagePreview, Loading } from '@arco-design/mobile-react';
import Taro from '@tarojs/taro';
import './index.less';

// 组件
import YkNavBar from '@/components/ykNavBar';
import Msgx from '@/components/Msgx';

// API
import {
  getAiChatConversationList,
  createAiChatConversation,
  getAiChatMessagePage,
  sendAiChatMessageStream
} from '@/utils/api/common/common_user';

// 类型定义
import {
  ChatPageState,
  ChatMessage,
  Conversation,
  UserInfo,
  MessageType,
  SenderType,
  SSEMessageData
} from './types';

// 调试工具
import { createDebugger, BaseDebugScenario } from '@/pages/debug/utils';

// 调试配置
const debugConfig = {
  moduleName: 'AiChat',
  scenarios: {
    [BaseDebugScenario.BASIC]: {},
    [BaseDebugScenario.API_CALLS]: {},
    [BaseDebugScenario.MOCK_DATA]: {},
    [BaseDebugScenario.LOADING_STATE]: {},
    [BaseDebugScenario.ERROR_HANDLING]: {},
    // 自定义场景
    'sse_test': { description: 'SSE流式接口测试' },
    'keyboard_test': { description: '键盘适配测试' },
    'scroll_test': { description: '滚动功能测试' }
  },
  mockDataGenerators: {
    [BaseDebugScenario.MOCK_DATA]: () => ({
      conversations: [
        { id: 'mock_conv_1', title: '模拟对话1', createTime: Date.now() },
        { id: 'mock_conv_2', title: '模拟对话2', createTime: Date.now() }
      ],
      messages: [
        {
          id: 'mock_msg_1',
          type: MessageType.TEXT,
          content: '您好！我是AI客服，有什么可以帮助您的吗？',
          sender: SenderType.AI,
          timestamp: Date.now() - 60000,
          avatar: '/src/assets/images/common/kefu_headimg.png'
        },
        {
          id: 'mock_msg_2',
          type: MessageType.TEXT,
          content: '我想了解一下产品信息',
          sender: SenderType.USER,
          timestamp: Date.now() - 30000,
          avatar: ''
        }
      ]
    }),
    'sse_test': () => ({
      testMessages: [
        '这是第一段回复',
        '这是第二段回复',
        '这是第三段回复，内容比较长一些，用来测试流式输出的效果'
      ]
    })
  }
};

const aiChatDebugger = createDebugger(debugConfig);

export default function AiChatPage() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<ChatPageState>({
    loading: false,
    conversationId: null,
    messages: [],
    inputText: '',
    keyboardHeight: 0,
    sending: false,
    initialized: false
  });

  const [userInfo, setUserInfo] = useState<UserInfo>({
    user_id: '',
    nickName: ''
  });

  // ==================== Refs ====================
  const scrollViewRef = useRef<any>(null);
  const sseClientRef = useRef<any>(null);

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
    setupKeyboardListener();
    
    return () => {
      cleanupSSEConnection();
      cleanupKeyboardListener();
    };
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    aiChatDebugger.debugLog('[basic] 页面初始化开始');

    // 调试模式下显示当前场景
    if (aiChatDebugger.isDebugMode()) {
      const scenario = aiChatDebugger.getDebugScenario();
      aiChatDebugger.debugLog(`[basic] 调试模式已启用，当前场景: ${scenario}`);

      // 特殊场景处理
      if (scenario === BaseDebugScenario.LOADING_STATE) {
        setPageState(prev => ({ ...prev, loading: true }));
        // 模拟长时间加载
        setTimeout(() => {
          setPageState(prev => ({ ...prev, loading: false }));
        }, 5000);
        return;
      }
    }

    setPageState(prev => ({ ...prev, loading: true }));

    try {
      // 1. 获取用户信息
      const storageUserInfo = Taro.getStorageSync('userInfo');
      if (storageUserInfo?.userInfo) {
        setUserInfo(storageUserInfo.userInfo);
        aiChatDebugger.debugLog('[basic] 用户信息获取成功', storageUserInfo.userInfo);
      } else {
        // 调试模式下提供默认用户信息
        if (aiChatDebugger.isDebugMode()) {
          const mockUserInfo = {
            user_id: 'debug_user_123',
            nickName: '调试用户',
            head_img: ''
          };
          setUserInfo(mockUserInfo);
          aiChatDebugger.debugLog('[basic] 使用调试用户信息', mockUserInfo);
        }
      }

      // 2. 获取或创建对话
      await initConversation();

      setPageState(prev => ({ ...prev, initialized: true }));
      aiChatDebugger.debugLog('[basic] 页面初始化完成');
    } catch (error) {
      console.error('页面初始化失败:', error);
      aiChatDebugger.debugLog('[error] 页面初始化失败', error);

      // 调试模式下的错误处理
      if (aiChatDebugger.isDebugMode() && aiChatDebugger.getDebugScenario() === BaseDebugScenario.ERROR_HANDLING) {
        aiChatDebugger.debugLog('[error] 调试模式：模拟错误恢复');
        setPageState(prev => ({ ...prev, initialized: true }));
        return;
      }

      Taro.showToast({
        title: '页面加载失败',
        icon: 'error'
      });
    } finally {
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 对话初始化 ====================
  const initConversation = async () => {
    try {
      aiChatDebugger.debugLog('[api] 获取对话列表');

      // 调试模式下可以使用模拟数据
      if (aiChatDebugger.isDebugMode() && aiChatDebugger.getDebugScenario() === BaseDebugScenario.MOCK_DATA) {
        const mockConversationId = 'mock_conversation_123';
        setPageState(prev => ({ ...prev, conversationId: mockConversationId }));
        aiChatDebugger.debugLog('[mock] 使用模拟对话ID', { id: mockConversationId });

        // 加载模拟消息
        const mockMessages: ChatMessage[] = [
          {
            id: '1',
            type: MessageType.TEXT,
            content: '您好！我是AI客服，有什么可以帮助您的吗？',
            sender: SenderType.AI,
            timestamp: Date.now() - 60000,
            avatar: '/src/assets/images/common/kefu_headimg.png'
          }
        ];
        setPageState(prev => ({ ...prev, messages: mockMessages }));
        setTimeout(scrollToBottom, 100);
        return;
      }

      // 获取对话列表
      const conversationResponse = await getAiChatConversationList();

      if (conversationResponse.code === 0 && conversationResponse.data?.length > 0) {
        // 使用第一个对话
        const conversation = conversationResponse.data[0];
        setPageState(prev => ({ ...prev, conversationId: conversation.id }));
        aiChatDebugger.debugLog('[api] 使用现有对话', conversation);

        // 加载对话消息
        await loadMessages(conversation.id);
      } else {
        // 创建新对话
        aiChatDebugger.debugLog('[api] 创建新对话');
        const createResponse = await createAiChatConversation();

        if (createResponse.code === 0) {
          const newConversationId = createResponse.data.id;
          setPageState(prev => ({ ...prev, conversationId: newConversationId }));
          aiChatDebugger.debugLog('[api] 新对话创建成功', { id: newConversationId });
        } else {
          throw new Error(createResponse.msg || '创建对话失败');
        }
      }
    } catch (error) {
      console.error('对话初始化失败:', error);
      aiChatDebugger.debugLog('[error] 对话初始化失败', error);

      // 调试模式下提供降级方案
      if (aiChatDebugger.isDebugMode()) {
        const fallbackConversationId = 'fallback_conversation_' + Date.now();
        setPageState(prev => ({ ...prev, conversationId: fallbackConversationId }));
        aiChatDebugger.debugLog('[error] 使用降级对话ID', { id: fallbackConversationId });
        return;
      }

      throw error;
    }
  };

  // ==================== 加载消息 ====================
  const loadMessages = async (conversationId: string) => {
    try {
      aiChatDebugger.debugLog('[api] 加载消息', { conversationId });
      
      const response = await getAiChatMessagePage({
        pageNo: 1,
        pageSize: 50,
        conversationId: parseInt(conversationId),
        userId: parseInt(userInfo.user_id)
      });

      if (response.code === 0 && response.data?.list) {
        const messages: ChatMessage[] = response.data.list.map((msg: any) => ({
          id: msg.id.toString(),
          type: MessageType.TEXT,
          content: msg.content,
          sender: msg.type === 'user' ? SenderType.USER : SenderType.AI,
          timestamp: msg.createTime,
          avatar: msg.type === 'user' ? userInfo.head_img : '/src/assets/images/common/kefu_headimg.png'
        }));

        setPageState(prev => ({ ...prev, messages }));
        aiChatDebugger.debugLog('[api] 消息加载成功', { count: messages.length });
        
        // 滚动到底部
        setTimeout(scrollToBottom, 100);
      }
    } catch (error) {
      console.error('加载消息失败:', error);
      aiChatDebugger.debugLog('[error] 加载消息失败', error);
    }
  };

  // ==================== 发送消息 ====================
  const sendMessage = async () => {
    const message = pageState.inputText.trim();
    if (!message || pageState.sending || !pageState.conversationId) {
      return;
    }

    aiChatDebugger.debugLog('[api] 发送消息', { message, conversationId: pageState.conversationId });
    
    // 添加用户消息到界面
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: MessageType.TEXT,
      content: message,
      sender: SenderType.USER,
      timestamp: Date.now(),
      avatar: userInfo.head_img
    };

    setPageState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      inputText: '',
      sending: true
    }));

    // 添加AI加载消息
    const loadingMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: MessageType.TEXT,
      content: '',
      sender: SenderType.AI,
      timestamp: Date.now(),
      avatar: '/src/assets/images/common/kefu_headimg.png',
      loading: true
    };

    setPageState(prev => ({
      ...prev,
      messages: [...prev.messages, loadingMessage]
    }));

    scrollToBottom();

    try {
      // 发送流式消息
      await sendStreamMessage(message, loadingMessage.id);
    } catch (error) {
      console.error('发送消息失败:', error);
      aiChatDebugger.debugLog('[error] 发送消息失败', error);
      
      // 移除加载消息，显示错误
      setPageState(prev => ({
        ...prev,
        messages: prev.messages.filter(msg => msg.id !== loadingMessage.id),
        sending: false
      }));
      
      Taro.showToast({
        title: '发送失败',
        icon: 'error'
      });
    }
  };

  // ==================== 流式消息发送 ====================
  const sendStreamMessage = async (content: string, loadingMessageId: string) => {
    aiChatDebugger.debugLog('[api] 开始流式消息发送', { content, conversationId: pageState.conversationId });

    try {
      // 创建SSE连接
      const sseUrl = '/app-api/ai/chat/message/send-stream';
      const requestData = {
        conversationId: pageState.conversationId,
        content: content,
        useContext: true
      };

      // 使用fetch实现SSE
      const response = await fetch(sseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'tenant-id': '1',
          'Authorization': Taro.getStorageSync('userInfo')?.accessToken || ''
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let aiResponseContent = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data:')) {
              try {
                const jsonStr = line.slice(5).trim();
                if (jsonStr && jsonStr !== '[DONE]') {
                  const data: SSEMessageData = JSON.parse(jsonStr);

                  if (data.code === 0 && data.data?.receive?.content) {
                    aiResponseContent += data.data.receive.content;

                    // 实时更新AI消息内容
                    setPageState(prev => ({
                      ...prev,
                      messages: prev.messages.map(msg =>
                        msg.id === loadingMessageId
                          ? { ...msg, content: aiResponseContent, loading: false }
                          : msg
                      )
                    }));

                    scrollToBottom();
                  }
                }
              } catch (parseError) {
                console.warn('解析SSE数据失败:', parseError);
              }
            }
          }
        }
      }

      setPageState(prev => ({ ...prev, sending: false }));
      aiChatDebugger.debugLog('[api] 流式消息发送完成', { finalContent: aiResponseContent });

    } catch (error) {
      console.error('SSE连接失败，使用模拟回复:', error);
      aiChatDebugger.debugLog('[error] SSE连接失败，使用模拟回复', error);

      // 降级到模拟回复
      setTimeout(() => {
        const aiResponse = `收到您的消息："${content}"。这是AI的回复示例。`;

        setPageState(prev => ({
          ...prev,
          messages: prev.messages.map(msg =>
            msg.id === loadingMessageId
              ? { ...msg, content: aiResponse, loading: false }
              : msg
          ),
          sending: false
        }));

        scrollToBottom();
        aiChatDebugger.debugLog('[api] 模拟回复完成', { response: aiResponse });
      }, 1500);
    }
  };

  // ==================== 滚动到底部 ====================
  const scrollToBottom = () => {
    aiChatDebugger.debugLog('[scroll] 滚动到底部');
    setTimeout(() => {
      if (scrollViewRef.current) {
        // 尝试多种滚动方法
        if (scrollViewRef.current.scrollToBottom) {
          scrollViewRef.current.scrollToBottom();
        } else if (scrollViewRef.current.scrollTo) {
          scrollViewRef.current.scrollTo({ top: 999999, animated: true });
        } else {
          // 降级方案：直接操作DOM
          const scrollElement = document.querySelector('.message-list');
          if (scrollElement) {
            scrollElement.scrollTop = scrollElement.scrollHeight;
          }
        }
      }
    }, 100);
  };

  // ==================== 键盘监听 ====================
  const setupKeyboardListener = () => {
    aiChatDebugger.debugLog('[keyboard] 设置键盘监听');

    // Taro的键盘监听
    if (Taro.onKeyboardHeightChange) {
      try {
        Taro.onKeyboardHeightChange((res) => {
          aiChatDebugger.debugLog('[keyboard] 键盘高度变化', res);
          setPageState(prev => ({ ...prev, keyboardHeight: res.height }));
          if (res.height > 0) {
            setTimeout(scrollToBottom, 200);
          }
        });
        aiChatDebugger.debugLog('[keyboard] Taro键盘监听设置成功');
      } catch (error) {
        aiChatDebugger.debugLog('[keyboard] Taro键盘监听设置失败，降级到H5方案', error);
        // 如果Taro的键盘监听失败，直接使用H5方案
      }
    } else {
      aiChatDebugger.debugLog('[keyboard] Taro.onKeyboardHeightChange API不存在，使用H5方案');
    }

    // H5环境下的键盘监听
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        const visualViewportHeight = window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const currentKeyboardHeight = Math.max(0, windowHeight - visualViewportHeight);

        aiChatDebugger.debugLog('[keyboard] H5键盘高度变化', {
          visualViewportHeight,
          windowHeight,
          currentKeyboardHeight
        });

        setPageState(prev => ({ ...prev, keyboardHeight: currentKeyboardHeight }));

        if (currentKeyboardHeight > 0) {
          setTimeout(scrollToBottom, 200);
        }
      };

      // 监听视觉视口变化
      if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', handleResize);
        window.visualViewport.addEventListener('scroll', handleResize);
      }

      // 监听窗口大小变化
      window.addEventListener('resize', handleResize);

      // 存储清理函数
      (window as any).__keyboardCleanup = () => {
        if (window.visualViewport) {
          window.visualViewport.removeEventListener('resize', handleResize);
          window.visualViewport.removeEventListener('scroll', handleResize);
        }
        window.removeEventListener('resize', handleResize);
      };
    }
  };

  const cleanupKeyboardListener = () => {
    aiChatDebugger.debugLog('[keyboard] 清理键盘监听');

    if (Taro.offKeyboardHeightChange) {
      try {
        Taro.offKeyboardHeightChange();
      } catch (error) {
        aiChatDebugger.debugLog('[keyboard] 清理Taro键盘监听失败', error);
      }
    }

    if (typeof window !== 'undefined' && (window as any).__keyboardCleanup) {
      (window as any).__keyboardCleanup();
      delete (window as any).__keyboardCleanup;
    }
  };

  // ==================== SSE连接清理 ====================
  const cleanupSSEConnection = () => {
    if (sseClientRef.current) {
      sseClientRef.current.close?.();
      sseClientRef.current = null;
    }
  };

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const handleInputChange = (value: string) => {
    setPageState(prev => ({ ...prev, inputText: value }));
  };

  const handleKeyPress = (e: any) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // ==================== 渲染消息 ====================
  const renderMessage = (message: ChatMessage, index: number) => {
    const isUser = message.sender === SenderType.USER;
    const showTime = index === 0 || 
      (pageState.messages[index - 1] && 
       message.timestamp - pageState.messages[index - 1].timestamp > 300000); // 5分钟

    return (
      <View key={message.id} className="message-item">
        {showTime && (
          <View className="message-time">
            {new Date(message.timestamp).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </View>
        )}
        <View className={`message-content ${isUser ? 'user-message' : 'ai-message'}`}>
          <Avatar 
            src={message.avatar} 
            className="message-avatar"
            size={40}
          />
          <View className="message-bubble">
            {message.loading ? (
              <View className="message-loading">
                <Loading size="small" />
                <Text>AI正在思考...</Text>
              </View>
            ) : (
              <Text className="message-text">{message.content}</Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  // ==================== 渲染 ====================
  if (pageState.loading) {
    return (
      <View className="ai-chat-page">
        <YkNavBar title="AI客服" onClickLeft={handleBack} />
        <View className="loading-container">
          <Loading />
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="ai-chat-page">
      <YkNavBar title="AI客服" onClickLeft={handleBack} />
      
      {/* 消息列表 */}
      <View className="chat-container">
        <ScrollView
          ref={scrollViewRef}
          className="message-list"
          scrollY
          scrollIntoView=""
          enableBackToTop
          scrollWithAnimation
          enhanced
          showScrollbar={false}
          onScrollToLower={() => {
            aiChatDebugger.debugLog('[scroll] 滚动到底部触发');
          }}
          onScroll={(e) => {
            // 可以在这里处理滚动事件，比如加载更多历史消息
            const { scrollTop, scrollHeight, clientHeight } = e.detail;
            if (scrollTop === 0 && pageState.messages.length > 0) {
              aiChatDebugger.debugLog('[scroll] 滚动到顶部，可以加载更多历史消息');
            }
          }}
        >
          {pageState.messages.length === 0 ? (
            <View className="empty-messages">
              <Text>开始与AI客服对话吧！</Text>
            </View>
          ) : (
            pageState.messages.map((message, index) => renderMessage(message, index))
          )}
        </ScrollView>
      </View>

      {/* 输入框 */}
      <View 
        className="input-container"
        style={{
          bottom: pageState.keyboardHeight > 0 ? `${pageState.keyboardHeight}px` : '0px'
        }}
      >
        <View className="input-wrapper">
          <Textarea
            className="message-input"
            placeholder="请输入您的问题..."
            value={pageState.inputText}
            onChange={handleInputChange}
            onKeyUp={handleKeyPress}
            maxLength={500}
            showStatistics={false}
            border="none"
            autoHeight
            disabled={pageState.sending}
          />
          <Button
            type="primary"
            size="small"
            className={`send-button ${pageState.inputText.trim() ? 'active' : ''}`}
            onClick={sendMessage}
            loading={pageState.sending}
            disabled={!pageState.inputText.trim() || pageState.sending}
          >
            发送
          </Button>
        </View>
      </View>

      {/* Debug信息 */}
      {aiChatDebugger.isDebugMode() && (
        <View className="debug-panel">
          <Msgx
            direction="topCenter"
            content={`调试模式: ${aiChatDebugger.getDebugScenario()}`}
            visible={true}
            backgroundColor="#007AFF"
            textColor="#fff"
          >
            <View className="debug-indicator">DEBUG</View>
          </Msgx>

          {/* 调试按钮组 */}
          <View className="debug-buttons">
            <Button
              size="mini"
              onClick={() => {
                const mockMessage: ChatMessage = {
                  id: Date.now().toString(),
                  type: MessageType.TEXT,
                  content: '这是一条测试消息',
                  sender: SenderType.USER,
                  timestamp: Date.now(),
                  avatar: userInfo.head_img
                };
                setPageState(prev => ({ ...prev, messages: [...prev.messages, mockMessage] }));
                scrollToBottom();
                aiChatDebugger.debugLog('[debug] 添加测试消息', mockMessage);
              }}
            >
              添加测试消息
            </Button>

            <Button
              size="mini"
              onClick={() => {
                const aiMessage: ChatMessage = {
                  id: Date.now().toString(),
                  type: MessageType.TEXT,
                  content: '这是AI的测试回复，内容比较长，用来测试消息显示效果和自动换行功能。',
                  sender: SenderType.AI,
                  timestamp: Date.now(),
                  avatar: '/src/assets/images/common/kefu_headimg.png'
                };
                setPageState(prev => ({ ...prev, messages: [...prev.messages, aiMessage] }));
                scrollToBottom();
                aiChatDebugger.debugLog('[debug] 添加AI测试消息', aiMessage);
              }}
            >
              添加AI消息
            </Button>

            <Button
              size="mini"
              onClick={() => {
                setPageState(prev => ({ ...prev, messages: [] }));
                aiChatDebugger.debugLog('[debug] 清空消息列表');
              }}
            >
              清空消息
            </Button>

            <Button
              size="mini"
              onClick={() => {
                const newHeight = pageState.keyboardHeight > 0 ? 0 : 300;
                setPageState(prev => ({ ...prev, keyboardHeight: newHeight }));
                aiChatDebugger.debugLog('[debug] 切换键盘状态', { height: newHeight });
                if (newHeight > 0) {
                  setTimeout(scrollToBottom, 100);
                }
              }}
            >
              模拟键盘
            </Button>

            <Button
              size="mini"
              onClick={() => {
                const loadingMessage: ChatMessage = {
                  id: Date.now().toString(),
                  type: MessageType.TEXT,
                  content: '',
                  sender: SenderType.AI,
                  timestamp: Date.now(),
                  avatar: '/src/assets/images/common/kefu_headimg.png',
                  loading: true
                };
                setPageState(prev => ({ ...prev, messages: [...prev.messages, loadingMessage] }));
                scrollToBottom();
                aiChatDebugger.debugLog('[debug] 添加加载消息', loadingMessage);

                // 3秒后移除加载状态
                setTimeout(() => {
                  setPageState(prev => ({
                    ...prev,
                    messages: prev.messages.map(msg =>
                      msg.id === loadingMessage.id
                        ? { ...msg, content: '模拟AI回复完成', loading: false }
                        : msg
                    )
                  }));
                }, 3000);
              }}
            >
              测试加载
            </Button>

            <Button
              size="mini"
              onClick={() => {
                scrollToBottom();
                aiChatDebugger.debugLog('[debug] 手动滚动到底部');
              }}
            >
              滚动到底部
            </Button>
          </View>
        </View>
      )}
    </View>
  );
}
