@import '@arco-design/mobile-react/style/mixin.less';
@import "@/utils/css/variables.less";

// 页面根容器样式
[id^="/pages/my/ai/chat/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

// AI聊天页面主容器
.ai-chat-page {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: var(--dark-background-color) !important;
  });

  // 加载容器
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 16px;
    
    .arco-loading {
      color: var(--primary-color);
    }
    
    text {
      font-size: 14px;
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-font-color) !important;
      });
    }
  }

  // 聊天容器
  .chat-container {
    flex: 1;
    overflow: hidden;
    padding: 0 16px;
    
    .message-list {
      height: 100%;
      padding: 16px 0;

      .empty-messages {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;

        text {
          font-size: 14px;
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
        }
      }
    }
  }

  // 消息项
  .message-item {
    margin-bottom: 16px;
    
    // 时间显示
    .message-time {
      text-align: center;
      margin-bottom: 12px;
      font-size: 12px;
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
    }
    
    // 消息内容
    .message-content {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      
      // 用户消息（右侧）
      &.user-message {
        flex-direction: row-reverse;
        
        .message-bubble {
          background-color: var(--primary-color);
          .use-dark-mode-query({
            background-color: var(--dark-primary-color) !important;
          });
          
          .message-text {
            color: #fff;
          }
          
          // 气泡箭头
          &::before {
            content: '';
            position: absolute;
            right: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border-left: 8px solid var(--primary-color);
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            .use-dark-mode-query({
              border-left-color: var(--dark-primary-color) !important;
            });
          }
        }
      }
      
      // AI消息（左侧）
      &.ai-message {
        .message-bubble {
          .use-var(background-color, container-background-color);
          .use-dark-mode-query({
            background-color: var(--dark-container-background-color) !important;
          });
          border: 1px solid var(--line-color);
          .use-dark-mode-query({
            border-color: var(--dark-line-color) !important;
          });
          
          .message-text {
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: var(--dark-font-color) !important;
            });
          }
          
          // 气泡箭头
          &::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border-right: 8px solid var(--container-background-color);
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            .use-dark-mode-query({
              border-right-color: var(--dark-container-background-color) !important;
            });
          }
          
          &::after {
            content: '';
            position: absolute;
            left: -9px;
            top: 12px;
            width: 0;
            height: 0;
            border-right: 8px solid var(--line-color);
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            .use-dark-mode-query({
              border-right-color: var(--dark-line-color) !important;
            });
          }
        }
      }
    }
    
    // 头像
    .message-avatar {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
    }
    
    // 消息气泡
    .message-bubble {
      position: relative;
      max-width: 70%;
      padding: 12px 16px;
      border-radius: 16px;
      word-wrap: break-word;
      word-break: break-all;
      
      .message-text {
        font-size: 15px;
        line-height: 22px;
        margin: 0;
      }
      
      // 加载状态
      .message-loading {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .arco-loading {
          color: var(--sub-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-font-color) !important;
          });
        }
        
        text {
          font-size: 14px;
          .use-var(color, sub-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-font-color) !important;
          });
        }
      }
    }
  }

  // 输入容器
  .input-container {
    position: fixed;
    left: 0;
    right: 0;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: var(--dark-background-color) !important;
    });
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
      border-top-color: var(--dark-line-color) !important;
    });
    padding: 12px 16px;
    transition: bottom 0.3s ease;
    
    .input-wrapper {
      display: flex;
      align-items: flex-end;
      gap: 12px;
      
      .message-input {
        flex: 1;
        min-height: 40px;
        max-height: 120px;
        .use-var(background-color, container-background-color);
        .use-dark-mode-query({
          background-color: var(--dark-container-background-color) !important;
        });
        border: 1px solid var(--line-color);
        .use-dark-mode-query({
          border-color: var(--dark-line-color) !important;
        });
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 15px;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: var(--dark-font-color) !important;
        });
        
        &::placeholder {
          .use-var(color, sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
        }
        
        &:focus {
          border-color: var(--primary-color);
          .use-dark-mode-query({
            border-color: var(--dark-primary-color) !important;
          });
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
      
      .send-button {
        flex-shrink: 0;
        height: 40px;
        padding: 0 20px;
        border-radius: 20px;
        font-size: 14px;
        transition: all 0.3s ease;
        
        &:not(.active) {
          background-color: var(--line-color);
          .use-dark-mode-query({
            background-color: var(--dark-line-color) !important;
          });
          color: var(--sub-info-font-color);
          .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
          border: none;
        }
        
        &.active {
          background-color: var(--primary-color);
          .use-dark-mode-query({
            background-color: var(--dark-primary-color) !important;
          });
          color: #fff;
          border: none;
          
          &:hover {
            opacity: 0.9;
          }
        }
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  // 调试面板
  .debug-panel {
    position: fixed;
    top: 80px;
    right: 16px;
    z-index: 1000;

    .debug-indicator {
      background-color: #007AFF;
      color: #fff;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }

    .debug-buttons {
      margin-top: 8px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .arco-btn {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
        min-height: 24px;
        background-color: rgba(0, 122, 255, 0.1);
        border: 1px solid #007AFF;
        color: #007AFF;

        &:hover {
          background-color: rgba(0, 122, 255, 0.2);
        }

        &:active {
          background-color: rgba(0, 122, 255, 0.3);
        }
      }
    }
  }
}
