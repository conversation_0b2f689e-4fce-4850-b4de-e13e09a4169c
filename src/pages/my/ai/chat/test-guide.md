# AI客服聊天页面测试指南

## 快速测试

### 1. 基础功能测试

访问页面：
```
/pages/my/ai/chat/index
```

测试步骤：
1. 页面加载是否正常
2. 导航栏显示是否正确
3. 输入框是否可以正常输入
4. 发送按钮状态是否正确（有内容时激活）
5. 消息是否正常发送和显示

### 2. 调试模式测试

#### 基础调试模式
```
/pages/my/ai/chat/index?debug
```
- 右上角应显示DEBUG指示器
- 显示调试按钮组
- 控制台输出调试日志

#### 模拟数据模式
```
/pages/my/ai/chat/index?debug=mock
```
- 使用模拟对话数据
- 显示预设的欢迎消息
- 不调用真实API

#### API调试模式
```
/pages/my/ai/chat/index?debug=api
```
- 显示详细的API调用日志
- 记录请求和响应数据

#### 加载状态测试
```
/pages/my/ai/chat/index?debug=loading
```
- 模拟长时间加载状态
- 测试加载动画显示

#### 错误处理测试
```
/pages/my/ai/chat/index?debug=error
```
- 模拟各种错误场景
- 测试错误恢复机制

### 3. 键盘适配测试

#### 移动端测试
1. 点击输入框
2. 观察键盘弹起时页面布局
3. 输入框应固定在键盘上方
4. 消息列表应自动滚动

#### H5环境测试
1. 使用浏览器开发者工具模拟移动设备
2. 测试虚拟键盘功能
3. 检查视觉视口变化处理

### 4. 消息功能测试

#### 发送消息
1. 输入文本消息
2. 点击发送按钮
3. 检查消息是否正确显示
4. 检查AI回复是否正常

#### 流式消息测试
1. 发送较长的问题
2. 观察AI回复的流式输出效果
3. 检查加载状态显示

#### 消息滚动测试
1. 发送多条消息
2. 检查自动滚动到底部
3. 手动滚动到顶部测试

## 调试按钮功能

### 添加测试消息
- 快速添加用户测试消息
- 测试消息显示效果

### 添加AI消息
- 添加AI测试回复
- 测试长文本显示

### 清空消息
- 清空所有消息
- 测试空状态显示

### 模拟键盘
- 切换键盘弹起/收起状态
- 测试布局适配

### 测试加载
- 添加加载状态消息
- 3秒后自动完成

### 滚动到底部
- 手动触发滚动
- 测试滚动功能

## 常见问题排查

### 1. 页面无法加载
- 检查路由配置
- 检查组件导入
- 查看控制台错误

### 2. API调用失败
- 检查网络连接
- 验证用户登录状态
- 检查API权限配置

### 3. 键盘适配异常
- 检查平台环境
- 验证事件监听器
- 测试不同设备

### 4. 消息显示异常
- 检查数据格式
- 验证类型定义
- 查看样式问题

### 5. 调试功能不工作
- 确认URL参数正确
- 检查调试工具导入
- 验证环境配置

## 性能测试

### 1. 消息数量测试
- 发送大量消息
- 观察渲染性能
- 检查内存使用

### 2. 长时间使用测试
- 持续使用30分钟
- 检查内存泄漏
- 验证事件清理

### 3. 网络异常测试
- 断网重连测试
- 弱网环境测试
- 超时处理测试

## 兼容性测试

### 浏览器兼容性
- Chrome (推荐)
- Safari
- Firefox
- Edge

### 移动设备兼容性
- iOS Safari
- Android Chrome
- 微信内置浏览器
- 支付宝内置浏览器

### 屏幕尺寸适配
- iPhone SE (375px)
- iPhone 12 (390px)
- iPhone 12 Pro Max (428px)
- iPad (768px)

## 测试清单

### 基础功能 ✅
- [ ] 页面正常加载
- [ ] 导航栏显示正确
- [ ] 输入框正常工作
- [ ] 发送按钮状态正确
- [ ] 消息正常发送
- [ ] AI回复正常显示

### 调试功能 ✅
- [ ] 调试模式激活
- [ ] 调试按钮工作
- [ ] 日志输出正常
- [ ] 模拟数据正确

### 键盘适配 ✅
- [ ] 键盘弹起适配
- [ ] 输入框位置正确
- [ ] 自动滚动工作
- [ ] 多平台兼容

### 消息功能 ✅
- [ ] 消息发送成功
- [ ] 流式回复正常
- [ ] 加载状态显示
- [ ] 消息滚动正常

### 错误处理 ✅
- [ ] 网络错误处理
- [ ] API失败降级
- [ ] 用户友好提示
- [ ] 错误恢复机制

### 性能表现 ✅
- [ ] 渲染性能良好
- [ ] 内存使用正常
- [ ] 无内存泄漏
- [ ] 响应速度快

## 报告问题

如果发现问题，请提供以下信息：
1. 问题描述
2. 复现步骤
3. 环境信息（浏览器、设备等）
4. 控制台错误信息
5. 调试日志（如果有）

## 优化建议

基于测试结果，可以考虑以下优化：
1. 消息虚拟化（大量消息时）
2. 图片懒加载
3. 离线消息缓存
4. 消息搜索功能
5. 语音输入支持
