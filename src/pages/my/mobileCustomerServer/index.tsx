import { useState, useEffect, useRef } from 'react';
import moment from 'moment';
import { IconPicture, IconSmileFill } from '@arco-design/mobile-react/esm/icon';
import { Textarea, Button, Avatar, Image, ImagePreview,Loading } from '@arco-design/mobile-react';
// import { Spin } from '@arco-design/web-react';
// import { LoadingOutlined } from '@arco-design/web-react/icon';
import { emojiList } from "@/utils/emoji";
import BottomPopup from "@/components/BottomPopup";
import './index.less';
import { View, ScrollView } from '@tarojs/components';
import { mobileScoket } from './socket';
import { sendMessageMobile, userRecord, serviceUpload } from './kefu_mobile';
import Taro from '@tarojs/taro';
import YkNavBar from '@/components/ykNavBar'
import { useSetPermission } from "@/stores/permissionStore";
import PermissionPopup from "@/components/PermissionPopup";
import { useDebounce } from "@/utils/yk-common";
// import data from '@emoji-mart/data'
// import Picker from '@emoji-mart/react'
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";

export default function MobileCustomerServer() {
  const [isLoad, setIsLoad] = useState(false);
  const [toChat, setToChat] = useState(false);
  const [userMessage, setUserMessage] = useState('');
  const [unReadMessage, setUnReadMessage] = useState(0);
  const [chatStatus, setChatStatus] = useState(false);
  const [pCont, setPCont] = useState('');
  const [previewIndex, setPreviewIndex] = useState(-1);
  const [isPopupVisible, setPopupVisible] = useState(false);
  const allPermissions = useRef<Permission[]>([]);
  const [permissionsState, setPermissionsState] = useState([]); // 用于存储并渲染权限数据
  const isRequestPermission = useRef(true);
  const platformRef = useRef("H5");
  const setPermission = useSetPermission();
  const [isPermissionPopupVisible, setPermissionPopupVisible] = useState(false); // 控制权限弹窗的显示状态
  const popupTitle = useRef("");
  const popupText = useRef("");
  const permissionHint = useRef(""); // 权限弹窗的说明
  const authConfirmType = useRef(-1);
  const [showEmoji, setShowEmoji] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const imageRefs = useRef([]);
  const [userInfo, setUserInfo] = useState({
    user_id: '',
    nickName: '',
  });

    // 添加 ChatServerData 接口定义
    interface ChatServerData {
      appid: string;
      avatar: string;
      nickname: string;
      to_user_avatar: string;
      to_user_nickname: string;
      to_user_id: string;
      uid: string;
      user_id: string;
      serviceList: ChatMessage[];
      welcome: string;
    }
  
    // 修改 useState 的类型声明
    const [chatServerData, setChatServerData] = useState<ChatServerData>({
      appid: '',
      avatar: '',
      nickname: '',
      to_user_avatar: '',
      to_user_nickname: '',
      to_user_id: '',
      uid: '',
      user_id: '',
      serviceList: [],
      welcome: ''
    });

  const records = chatServerData?.serviceList?.map((item, index) => {
    const newItem = { ...item };
    // 添加默认值处理
    newItem.time = item.add_time 
      ? moment(item.add_time * 1000).format('MM月DD日 H:mm')
      : moment().format('MM月DD日 H:mm');

    if (index) {
      if (
        item.add_time - chatServerData.serviceList[index - 1].add_time >= 300
      ) {
        newItem.show = true;
      } else {
        newItem.show = false;
      }
    } else {
      newItem.show = true;
    }
    return newItem;
  }) || [];

  // 获取所有图片消息
  const imageMessages = records.filter(item => item.msn_type === 3);
  const imageUrls = imageMessages.map(item => ({
    src: item.msn,
    fallbackSrc: item.msn
  }));
  
  const handleEmojiSelect = (emoji: string) => {
    setUserMessage(prev => prev + emoji);
    // setShowEmoji(false);
  };

  const selectEmoji = () => {
    // 只有在表情面板未打开时才隐藏键盘
    if (!showEmoji) {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement) {
            activeElement.blur(); // 关闭键盘
        }
    } else {
        // 如果表情面板已经打开，直接返回，不隐藏
        return;
    }
    setShowEmoji(!showEmoji); // 切换表情面板的显示状态
  };

// 添加图片选择相关方法
const openPopup = () => {
  setPopupVisible(true);
};

const handleClose = () => {
  setPopupVisible(false);
};

const requestPermissionWeb = (type) => {
  isRequestPermission.current = true;
  authConfirmType.current = type;
  if (platformRef.current === "Android") {
    window.checkPermission.checkPermission();
  } else {
    window.webkit.messageHandlers.requestPermission.postMessage(`${type}`);
  }
};

const handleConfirm = (index) => {
  if (index === 0) {
    // 请求相机权限
    if (!hasPermission(AuthTypes.CAMERA)) {
      requestPermissionWeb(AuthTypes.CAMERA);
      return;
    }
    chooseImage('camera');
  } else if (index === 1) {
    // 请求相册权限
    if (platformRef.current == "H5") {
      chooseImage('album');
    } else {
      if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
        requestPermissionWeb(AuthTypes.GALLERY_PHOTO);
        return;
      }
      chooseImage('album');
    }

  }
};

// 定义权限提示信息的映射
const permissionMapping = {
  [AuthTypes.CAMERA]: {
    hint: "在下方弹窗中选择允许后，你可以拍摄照片或视频以发送给朋友、使用视频通话等功能。你还可以在其他场景中访问摄像头进行拍摄照片和视频。",
  },
  [AuthTypes.GALLERY_PHOTO]: {
    hint: "在下方弹窗中选择允许后，你可以从手机相册中选择图片发送给朋友。",
  },
  [AuthTypes.GALLERY_VIDEO]: {
    hint: "在下方弹窗中选择允许后，你可以从手机相册中选择视频发送给朋友。",
  },
  [AuthTypes.GALLERY_AUDIO]: {
    hint: "在下方弹窗中选择允许后，你可以从手机相册中选择音频发送给朋友。",
  },
  [AuthTypes.STORAGE]: {
    hint: "在下方弹窗中选择允许后，你可以保存图片到相册。你还可以在其他场景访问设备里的照片视频和文件，以及保存内容到设备。",
  },
  [AuthTypes.AUDIO]: {
    hint: "录音权限说明",
  },
  [AuthTypes.NOTICE]: {
    hint: "通知权限说明",
  },
  [AuthTypes.LOCATION]: {
    hint: "定位权限说明",
  },
  [AuthTypes.CONTACT]: {
    hint: "通讯录权限说明",
  },
  [AuthTypes.FLOATWIN]: {},
  [AuthTypes.ACCESS]: {},
  [AuthTypes.AUTOSTART]: {},
};


// 权限回调处理
const checkPermissionCallBack = async (e) => {
  // 更新权限状态
  allPermissions.current = await setPermission(e); // 确保更新后赋值
  setPermissionsState([...allPermissions.current]); // 更新状态
  const currentAuth = authConfirmType.current;
  const permissionConfig = permissionMapping[currentAuth];
  if (isRequestPermission.current) {
    isRequestPermission.current = false;
    if (
      !permissionConfig &&
      currentAuth !== AuthTypes.FLOATWIN &&
      currentAuth !== AuthTypes.ACCESS &&
      currentAuth !== AuthTypes.AUTOSTART
    ) {
      console.error("Invalid authConfirmType:", currentAuth);
      return;
    }

    if (!hasPermission(currentAuth)) {
      // 设置权限提示信息（如果存在）
      if (permissionConfig.hint) {
        permissionHint.current = permissionConfig.hint;
        // 显示权限弹窗

        setPermissionPopupVisible(true);
      }
    }

    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(currentAuth + "");
    } else {
      if (hasPermission(currentAuth)) {
        webPermissonConsent();
      } else {
        webPermissonDeny();
      }
    }
  }
};

const hasPermission = (authType) => {
  const permission = allPermissions.current.find(
    (perm) => perm.authType === authType
  );
  console.log(allPermissions.current);
  return permission ? permission.state : false; // 如果找到了对应权限，返回权限状态；如果找不到，返回 false
};


const webPermissonDeny = () => {
  isRequestPermission.current = false;
  handlePermissionClose();
  // openSetting();
  // openSetPopup(clickItem.current);
};

const webPermissonConsent = () => {
  handlePermissionClose();
  if (platformRef.current === "Android") {
    window.checkPermission.checkPermission();
  } else {
    window.webkit.messageHandlers.checkPermission.postMessage("");
  }
};

// 关闭权限说明弹窗
const handlePermissionClose = () => {
  setPermissionPopupVisible(false);
};

const handlePermissionConfirm = (type) => {
  if (platformRef.current === "Android") {
    window.requestPermission.requestPermission(type);
  } else {
    window.webkit.messageHandlers.requestPermission.postMessage(type);
  }
};

const chooseImage = (sourceType: "album" | "camera") => {
  Taro.chooseImage({
    count: 1,
    sourceType: [sourceType],
    success: async (res) => {
      const imagePath = res.tempFilePaths[0];
      try {
        const response = await fetch(imagePath);
        const blob = await response.blob();
        const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });
        uploadImage(file);
      } catch (error) {
        console.error('处理图片失败:', error);
      }
    },
    fail: (err) => {
      console.error('选择图片失败:', err);
    },
  });
};

const uploadImage = async (file) => {
  const param = new FormData();
  param.append("filename", "file");
  param.append("file", file);
  
  try {
    const res = await serviceUpload(param);
    if (res.status === 200) {
      sendMsg(res.data.data.url, 3);
    }
  } catch (error) {
    console.error('上传图片失败:', error);
  }
};


    useEffect(() => {
      getUserRecord();
      const storageInfo = Taro.getStorageSync("userInfo");
      if (storageInfo) {
        setUserInfo(storageInfo.userInfo);
      }
      setTimeout(scrollToBottom, 100);
      let uaAll = window.navigator.userAgent;
      let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
      let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
  
      window.checkPermissionCallBack = checkPermissionCallBack;
      isRequestPermission.current = true;
      if (isAndroid) {
        platformRef.current = "Android";
        window.webPermissonDeny = webPermissonDeny;
        window.webPermissonConsent = webPermissonConsent;
  
        window.checkPermission.checkPermission();
      } else if (isIos) {
        platformRef.current = "IOS";
  
        window.webkit.messageHandlers.checkPermission.postMessage("");
      } else {
        platformRef.current = "H5";
      }
  
      return () => {
        delete window.checkPermissionCallBack;
        delete window.webPermissonDeny;
        delete window.webPermissonConsent;
      };
    }, []);

  const getUserRecord = () => {
    // TODO:看看要不要获取
    var upperData = {
      phone: '',
      sex: '',
      avatar: '',
      openid: '',
      kefu_id: '',
    }
    const info = Taro.getStorageSync("userInfo").userInfo;


    const postData = {
      uid: info.user_id,
      limit: 20,
      nickname: info.nickName,
      // phone: upperData?.phone,
      // sex: upperData?.sex,
      // avatar: upperData?.avatar,
      // openid: upperData?.openid,
      kefu_id: 1,
      // toUserId: info.user_id,
      // toUserId: getLoc('to_user_id') || 0,
      type: '0'
    };
    setIsLoad(true);
    userRecord(postData).then(res => {
      setChatServerData(res.data.data);
      setIsLoad(false);
      setUnReadMessage(0);
  

      if (res.data.data.welcome) {
        const welcomeMessage = {
          ...res.data.data.welcome,
          add_time: Date.parse(new Date().toString()) / 1000
        };
        setChatServerData(prev => ({
          ...prev,
          serviceList: [...prev.serviceList, welcomeMessage]
        }));
      }

      // document.title = res.data.to_user_nickname 
      //   ? `正在和${res.data.to_user_nickname}对话中 - ${res.data.site_name}` 
      //   : '正在和游客对话中 - ' + res.data.site_name;

      connentServer(res.data.data);
      scrollToBottom();
    }).catch(err => {
      setIsLoad(false);
      if (err.status === 400) {
        // navigate('/customer-outline', { 
        //   replace: true, 
        //   state: { ...props.location?.state }
        // });
      }
    });
  };


  const getGuid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

  // 添加类型定义
  interface ChatMessage {
    msn: string;
    msn_type: number;
    to_user_id: string;
    is_send: number;
    is_tourist: number;
    avatar: string;
    user_id: string;
    appid: string;
    type: number;
    guid: string;
    add_time: number;
    show: boolean;
    time: string;
  }

  interface ApiResponse {
    data: {
      autoReply: boolean;
      autoReplyData?: ChatMessage;
    };
  }

  const sendMsg = (msn: string, type: number) => {
    if(!chatStatus) {
      return;
    }
    
    const guid = getGuid();
    const chat: ChatMessage = {
      msn,
      msn_type: type,
      to_user_id: chatServerData?.to_user_id || '',
      is_send: 0,
      is_tourist: 0,
      avatar: chatServerData?.avatar || '',
      user_id: chatServerData?.user_id || '',
      appid: chatServerData?.appid || '',
      type: 0,
      guid,
      show: false,
      time: '',
      add_time: Date.parse(new Date().toString()) / 1000
    };

    sendMessageMobile(chat).then((res: ApiResponse) => {
      chat.add_time = Date.parse(new Date().toString()) / 1000;
      setChatServerData(prev => ({
        ...prev,
        serviceList: Array.isArray(prev.serviceList) ? [...prev.serviceList, chat] : [chat]
      }));
      scrollToBottom(); // 发送消息后滚动到底部

      if (res.data?.autoReply === true && res.data?.autoReplyData) {
        const autoReplyMessage: ChatMessage = {
          ...res.data.autoReplyData,
          add_time: Date.parse(new Date().toString()) / 1000
        };
        
        setChatServerData(prev => ({
          ...prev,
          serviceList: [...prev.serviceList, autoReplyMessage]
        }));
        scrollToBottom(); // 自动回复后滚动到底部

      }
    }).catch(error => {
      console.error('发送消息失败:', error);
      // 可以在这里添加错误提示
    });
  };

  const scrollToBottom = () => {
    console.log("触发scrollToBottom")
    const chatScroll = document.getElementById('chat_scroll');
    if (chatScroll) {
      setTimeout(() => {
        chatScroll.scrollTop = chatScroll.scrollHeight;
      }, 100);
    }
  };

 

  const textareaChange = (e) => {
    let strCont = e.target.value.replace(/\n\s(\s)*/gi, '\n');
    strCont = strCont.replace(/^\s*/gi, '');

    let strHtml = strCont.replace(/</gi, '&lt;');
    strHtml = strCont.replace(/\n(\n)*/gi, '<br>');
    strHtml = strHtml.replace(/\s+/gi, '&nbsp;');

    strCont = strHtml.replace(/&nbsp;/gi, ' ');
    strCont = strCont.replace(/<br>/gi, '\n');
    strCont = strCont.replace(/&lt;/gi, '<');

    setUserMessage(strCont);
    setPCont(strHtml + '.');
  };

  const replace_em = (str) => {
    return str.replace(/\[em-([\s\S]*)\]/g, "<span class='em em-$1'/></span>");
  };



  const sendText = () => {
    const sendMessage = userMessage.replace(/[\r\n]/g, '');
    
    if(sendMessage) {
      sendMsg(sendMessage, 1);
      setUserMessage('');
      setShowEmoji(false);
    }
  };

  const uploadFile = (e) => {
    // 实现文件上传逻辑
    openPopup()
  };

   // 添加滚动处理函数
   const scrollHandler = (e) => {
    if (!isLoad && chatServerData.serviceList?.length > 0 && e.target.scrollTop <= 5) {
      setIsLoad(true);
      
      const postData = {
        limit: 20,
        uid: chatServerData.uid,
        idTo: chatServerData.serviceList?.length > 0 ? chatServerData.serviceList[0].id : '',
        toUserId: chatServerData.to_user_id
      };
      
      userRecord(postData)
        .then(res => {
          if (res.status === 200) {
            setChatServerData(prev => ({
              ...prev,
              serviceList: [...res.data.data.serviceList, ...prev.serviceList]
            }));
          }
        })
        .catch(error => {
          console.error('加载历史消息失败:', error);
        })
        .finally(() => {
          setIsLoad(false);
        });
    }
  };

  const imageLoaded = (e, index) => {
    console.log(index)
    if (records.length < 20 && index < 20) {
      scrollToBottom()
    }
  }

  const connentServer = (serverData) => {
    const token = "f99daaddc7c3a45adb0384ed34acf489";
    const formTerminal = 'app';
    
    mobileScoket(true, token, formTerminal).then((ws) => {
      // wsRef.current = ws;
      
      ws.on('close', () => {
        setToChat(false);
        setChatStatus(false);
      });

      ws.on('kefu_logout', data => {
        if(data.online === 0) {
          // 处理客服离线逻辑
        }
      });

      // ws.on(['reply', 'chat'], data => {
      //   console.log('connentServer ws reply')
      //   setChatServerData(prev => ({
      //     ...prev,
      //     serviceList: [...prev.serviceList, data]
      //   }));
      // });

      ws.on('reply', data => {
        setChatServerData(prev => ({
          ...prev,
          serviceList: [...prev.serviceList, data]
        }));
        scrollToBottom(); // 自动回复后滚动到底部

      });

      ws.on('success', data => {
        setChatStatus(true);
        //TODO: 看看要不要处理
        var upperData = {
          isShowTip: "false",
        }
        const to_user_id = serverData?.to_user_id;
        const info = Taro.getStorageSync("userInfo").userInfo;

        ws.send({
          type: 'user',
          data: {
            to_user_id,
            uid: info.user_id,
            nickname: info?.nickname,
            avatar: info?.head_img,
            type: '0'
          }
        });

        if(!to_user_id && !toChat && serverData?.to_user_id) {
          ws.send({
            data: {
              id: serverData?.to_user_id,
              test: 1
            },
            type: "to_chat"
          });
          setToChat(true);
        }
      });

      ws.on('mssage_num', data => {
        if(data.num > 0) {
          setUnReadMessage(data.num);
        }
      });
    });
  };
  // 使用 useDebounce 包裹 scrollHandler
  const debouncedScrollHandler = useDebounce(scrollHandler, 500);

  useEffect(() => {
    // 监听键盘事件
    const handleResize = () => {
      const visualViewportHeight = window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      const currentKeyboardHeight = Math.max(0, windowHeight - visualViewportHeight);
      
      // 只在键盘高度为0时(键盘收起)才关闭表情面板
      if (currentKeyboardHeight === 0) {
      console.log('currentKeyboardHeight', currentKeyboardHeight)
        setShowEmoji(false);
      }

      setTimeout(() => {
        setKeyboardHeight(currentKeyboardHeight);


      }, 100);
    };

    // 添加事件监听
    window.visualViewport?.addEventListener('resize', handleResize);
    window.visualViewport?.addEventListener('scroll', handleResize);

    return () => {
        // 清理事件监听
        window.visualViewport?.removeEventListener('resize', handleResize);
        window.visualViewport?.removeEventListener('scroll', handleResize);
    };
  }, []);

  return (
    <View className="pc_customerServer_container" disableScroll={true}>
      <YkNavBar title="客服" />

      <View className="pc_customerServer_container_content">

        <ScrollView 
          className={'scroll_content'} 
          id="chat_scroll" 
          onScroll={debouncedScrollHandler}
        >

          {isLoad && (
                        <View style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Loading type="spin" />
                      </View>
          )}

          <View className="chart_list">
            {records.map((item, index) => (
              <View className="chart_list_item" key={index}>
                {item.show && <View className="chart_list_item_time">{item.time}</View>}
                <View className={`chart_list_item_content ${item.user_id === chatServerData.user_id ? 'right-box' : ''}`}>
                  <View className="chart_list_item_avatar">
                    <Avatar src={item.avatar} />
                  </View>
                  {item.msn_type <= 2 && (
                    <View className="chart_list_item_text">
                      <span dangerouslySetInnerHTML={{ __html: replace_em(item.msn) }} />
                    </View>
                  )}
                  {item.msn_type === 3 && (
                    <View className="chart_list_item_img">
                      {/* //onLoad={props.imageLoad}  */}
                      <Image src={item.msn} fit="cover" bottomOverlap={null} onClick={() => {
                        const imageIndex = imageMessages.findIndex(msg => msg.msn === item.msn);
                        setPreviewIndex(imageIndex);
                      }}
                      onLoad={(e) => imageLoaded(e,index)
                      }/>
                    </View>
                  )}
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>

      <View 
        className="footer_customerServer_container" 
        style={{ 
          bottom:  keyboardHeight > 0 ? `${keyboardHeight}px` :  showEmoji ? '200px' : '0px',
          position: 'fixed'
        }}
      >
        <View className="mobel_customerServer_container_footer">
          <View className="mobel_customerServer_container_footer_uploag_image">
            {/* <span className="iconfont">&#xe6ca;</span> */}
            <IconPicture onClick={uploadFile}></IconPicture>
            {/* <input type="file" className="file_input" onChange={uploadFile} /> */}
          </View>
          <View className="mobel_customerServer_container_footer_input">
            <View className="mobel_customerServer_container_footer_input_con">
              <Textarea
                onKeyUp={(e) => e.key === 'Enter' && sendText()}
                // onFocus={props.textareaInput}
                className="font"
                onChange={textareaChange}
                value={userMessage}
                placeholder="请输入内容"
                maxLength={500}
                showStatistics={false}
                border="none"
                // autosize
              />
              {/* <p className="font" dangerouslySetInnerHTML={{ __html: pCont }} /> */}
            </View>
          </View>
          <View className="mobel_customerServer_container_footer_emoji" onClick={selectEmoji}>
            <IconSmileFill useCurrentColor={true}/>
          </View>

          <View className={`sendMessage ${userMessage ? 'sendMessage-primary' : ''}`} onClick={sendText}>发送</View>

        </View>
      </View>

      {showEmoji && (
        <View 
          className="emoji-picker-container"
          style={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            background: '#fff',
            borderTop: '1px solid #eee',
            padding: '10px',
            height: '200px',
            zIndex: 99,
            
          }}
        >
          <View 
            className="emoji-list"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(8, 1fr)',
              gap: '10px',
              height: '100%',
              overflowY: 'auto'
            }}
          >
            {emojiList.map((emoji, index) => (
              <View 
                key={index} 
                className="emoji-item"
                style={{
                  textAlign: 'center',
                  cursor: 'pointer',
                  fontSize: '20px'
                }}
                onClick={() => handleEmojiSelect(emoji)}
              >
                {emoji}
              </View>
            ))}
          </View>
        </View>
      )}

      <ImagePreview
        openIndex={previewIndex}
        close={() => setPreviewIndex(-1)}
        images={imageUrls}
      />
      <BottomPopup
      options={["拍照", "从相册选择"]}
      btnCloseText="取消"
      onConfirm={handleConfirm}
      onClose={handleClose}
      visible={isPopupVisible}
    />

<PermissionPopup
          visible={isPermissionPopupVisible}
          type={authConfirmType.current}
          hintText={permissionHint.current}
          onClose={handlePermissionClose}
          onConfirm={handlePermissionConfirm}
        />
    </View>
  );
};