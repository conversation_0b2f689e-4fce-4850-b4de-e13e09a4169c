import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, Image, Dialog, ImagePreview, Carousel, Button, Avatar,ActionSheet, Input, Cell, Stepper, Textarea  } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { useState, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import ImagePreviewList from './components/ImagePreviewList';
import {  addCart  } from "@/utils/api/common/common_user";



import {
  dynamicDetails,
  deleteDynamic,
  dynamicRefresh,
  updateDynamic,
  dynamicBatchOperate,
  favoritesDynamic,
  deleteFavoritesDynamic,
  getUserHomeTopData
} from '@/utils/api/common/common_user';
import defaultHeadImg from '@/assets/images/common/default_head.png';
// import GoodCar from '@/components/goodCar';
import { navigateToVue } from '@/utils/yk-common';



export default function Detail() {
  // 状态定义
  const [showPage, setShowPage] = useState(false);
  const [dynamic, setDynamic] = useState<any>({id : ''});
  const [current, setCurrent] = useState(0);
  const [swiperList, setSwiperList] = useState([]);
  const [merchantUserInfo, setMerchantUserInfo] = useState({
    avatar: '',
    nickname: '',
    newNumbers: 0,
    total: 0
  });
  const [carListNum, setCarListNum] = useState(0);
  const [isCarShow, setIsCarShow] = useState(false);
  const [actionSheetVisible, setActionSheetVisible] = useState(false); // 1. 添加 state 控制显隐
  
  const goodCarRef = useRef(null);
  const userInfo = Taro.getStorageSync('userInfo') || {};
  const dynamicId = Taro.getCurrentInstance().router?.params?.dynamicId;
  const userId = Taro.getCurrentInstance().router?.params?.dynamicUserId;
  const [showImagePreview, setShowImagePreview] = useState(false);

  const [goodsCount, setGoodsCount] = useState(0);
  // 新增：用数组管理每个 Stepper 的数量 - 改为二维数组，第一维是颜色，第二维是规格
  const [stepperValues, setStepperValues] = useState<number[][]>([]);
  const [stepperNames, setStepperNames] = useState([]);
  // 添加备注状态
  const [remark, setRemark] = useState('');
  // 添加当前选中的颜色tab索引
  const [activeColorIndex, setActiveColorIndex] = useState(0);
  // 添加批量操作数量
  const [batchCount, setBatchCount] = useState(0);
  // 添加批量操作的上一个值，用于判断增减方向
  const [batchPrevValue, setBatchPrevValue] = useState(0);
  //const [format, setFormat] = useState([]);
  // 初始化加载
  useLoad(() => {

    setCarListNum(Taro.getStorageSync('goodCarNum') || 0);
    getDynamicDetails();
    getShopHomeInfo();
    console.log('userInfo', userInfo);
    console.log('userId from params:', userId);
    console.log('dynamicId from params:', dynamicId);
  });


  useEffect(() => {
    
  }, ['goodsCount']); // 组件挂载时执行一次

  // 初始化stepperValues数组
  const initStepperValues = () => {
    const colorNum = dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 创建二维数组：颜色数量 x 规格数量
    const newStepperValues = Array(colorNum).fill(null).map(() => Array(specificationsNum).fill(0));
    setStepperValues(newStepperValues);
  };

  // 获取动态详情
  const getDynamicDetails = async () => {
    try {
      if (!dynamicId) return;
      const res: any = await dynamicDetails({ id: dynamicId });
      console.log('res', res.data);
      if (res.code === 0) {
        setDynamic(res.data);
        if(res.data.pictures) {
          setSwiperList(res.data.pictures.split(','));
        }
        // 在设置动态数据后初始化stepperValues
        setTimeout(() => {
          setShowPage(true);
          // 延迟初始化，确保dynamic状态已更新
          setTimeout(() => {
            initStepperValues();
          }, 100);
        }, 1);
        console.log([ ...res.data.productSpecificationsNames.split(','), ...res.data.productColorNames.split(',')])
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
      Toast.error({ content: '获取详情失败' });
    }
  };

  // 获取商家信息
  const getShopHomeInfo = async () => {
    try {

      if (!userId) return;
      const res: any = await getUserHomeTopData({ userId: userId });
      console.log(res, '---------------');
      if (res.code === 0) {
        setMerchantUserInfo(res.data);

      }
    } catch (err) {
      console.error(err);
    }
  };

  const addCartData = async () => {
    const format: any[] = [];

    // 遍历每个颜色和规格的组合
    stepperValues.forEach((colorSpecs, colorIndex) => {
      colorSpecs.forEach((quantity, specIndex) => {
        if (quantity > 0) {
          format.push({
            productSpecificationsId: dynamic.productSpecificationsIds ? dynamic.productSpecificationsIds.split(',')[specIndex] : '',
            productColorId: dynamic.productColorIds ? dynamic.productColorIds.split(',')[colorIndex] : '',
            quantity: quantity,
            price: dynamic.price,
          });
        }
      });
    });

    // 检查是否有选择商品
    if (format.length === 0) {
      Toast.error({ content: '请选择商品规格或颜色' });
      return;
    }

    try {
      const data = {
        userId: userId,
        dynamicsId: dynamicId,
        remark: remark, // 添加备注字段
        type: 1,
        details: format,
      };
      console.log(data, 'data');
      const res: any = await addCart(data);
      if (res.code === 0) {
        Toast.success({ content: '加入购物车成功' });
        setCarListNum(carListNum + 1);
        Taro.setStorageSync('goodCarNum', carListNum + 1);
        // 重置表单
        initStepperValues();
        setRemark('');
        setActionSheetVisible(false);
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };
  

    // 添加前往商家主页方法
    const getIntoMerchantHomePage = () => {
        if (!userId) {
          Toast.error({ content: '用户信息获取失败' });
          return;
        }

        if (userId == userInfo?.userId) {
          navigateToVue('/pages/pagesA/albumManage/albumManage');
        } else {
          navigateToVue(`/pages/pagesA/userDetail/index?user_id=${userId}`);
        }
      };

  // 收藏/取消收藏
  const handleCollect = async () => {
    try {
      let res: any;
      if (dynamic.isCollect == 1) {
        res = await deleteFavoritesDynamic({ dynamicsId: dynamic.id, userId: userInfo.userId });
      } else {
        res = await favoritesDynamic({ dynamicsId: dynamic.id, userId: userInfo.userId });
      }
      if (res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isCollect = dynamic.isCollect === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isCollect === 1 ? '取消成功' : '收藏成功'
        });
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 置顶/取消置顶
  const handleTop = async () => {
    try {
      const res: any = await updateDynamic({
        isTop: dynamic.isTop === 1 ? 0 : 1,
        id: dynamic.id
      });
      if (res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isTop = dynamic.isTop === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isTop === 1 ? '取消置顶成功' : '置顶成功'
        });
        // 通知首页刷新数据
        Taro.eventCenter.trigger('refreshAlbumList');
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 下架商品
  const handleDown = () => {
    Dialog.confirm({
      title: "温馨提示",
      children: "确定要下架商品吗？",
      okText: "确定",
      cancelText: "取消",
      platform: "ios",
      onOk: async () => {
        try {
          const res: any = await updateDynamic({
            isListed: 2,
            id: dynamic.id
          });
          if (res.code === 0) {
            Toast.success({ content: '下架成功' });
            // 通知首页刷新数据
            Taro.eventCenter.trigger('refreshAlbumList');
            setTimeout(() => Taro.navigateBack(), 1500);
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 刷新
  const handleRefresh = async () => {
    try {
      await dynamicRefresh({ dynamic_id: dynamic.id });
      Toast.success({ content: '刷新成功' });
      // 通知首页刷新数据
      Taro.eventCenter.trigger('refreshAlbumList');
    } catch (err) {
      console.error(err);
    }
  };

  // 删除
  const handleDelete = () => {
    Dialog.confirm({
      title: "温馨提示",
      children: "删除图文后不可恢复，确定删除？",
      okText: "删除",
      cancelText: "取消",
      platform: "ios",
      onOk: async () => {
        try {
          const res: any = await deleteDynamic({ id: dynamic.id });
          if (res.code === 0) {
            Toast.success({ content: '删除成功' });
            // 通知首页刷新数据
            Taro.eventCenter.trigger('refreshAlbumList');
            setTimeout(() => Taro.navigateBack(), 1500);
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 编辑
  const handleEdit = () => {
    Taro.setStorageSync('releaseDynamicList', dynamic);
    Taro.navigateTo({
      url: `/pages/releaseDynamic/index?type=2`,

    });
  };

  // 批量操作：对当前颜色的所有规格进行加减
  const handleBatchOperation = (operation: 'add' | 'subtract') => {
    const newStepperValues = [...stepperValues];
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 确保当前颜色的数组存在
    if (!newStepperValues[activeColorIndex]) {
      newStepperValues[activeColorIndex] = Array(specificationsNum).fill(0);
    }

    // 直接对所有规格增加或减少1
    newStepperValues[activeColorIndex] = newStepperValues[activeColorIndex].map(value => {
      if (operation === 'add') {
        return Math.min(value + 1, 100);
      } else {
        return Math.max(value - 1, 0);
      }
    });

    setStepperValues(newStepperValues);
  };

  // 计算总数量和总价格
  const getTotalQuantityAndPrice = () => {
    let totalQuantity = 0;
    stepperValues.forEach(colorSpecs => {
      colorSpecs.forEach(quantity => {
        totalQuantity += quantity;
      });
    });
    const totalPrice = totalQuantity * (dynamic.price || 0);
    return { totalQuantity, totalPrice };
  };

  // 加入购物车
  const handleAddCar = () => {
    // 每次打开前重置
    const colorNum = dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 确保创建全新的二维数组，避免引用问题
    const newStepperValues: number[][] = [];
    for (let i = 0; i < Math.max(colorNum, 1); i++) {
      newStepperValues.push(new Array(specificationsNum).fill(0));
    }
    setStepperValues(newStepperValues);

    setRemark(''); // 重置备注
    setActiveColorIndex(0); // 重置到第一个颜色tab
    setBatchCount(0); // 重置批量数量
    setActionSheetVisible(true); // 3. 点击按钮时，只更新 state
  };

  // 前往购物车页面
  const goToCarPage = () => {
    if (!userInfo?.userId) {
      Toast.error({ content: '请先登录' });
      setTimeout(() => {

        Taro.navigateTo({ url: '/pages/login/index' });
      }, 1500);
      return;
    }
    navigateToVue("/pages/pagesA/car/index");
  };

  // 先将字符串转数组
  const productColorArr = dynamic.productColorNames ? dynamic.productColorNames.split(',') : [];
  const productSpecificationsArr = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',') : [];

  return (
    <View className="detailBox">
      <YkNavBar title={ dynamic.id ? `详情${dynamic.id}` : '详情'} />
      
      {showPage && (
        <>
          {/* 轮播图 */}
          {swiperList.length > 0 && (
            <View className="swiperList">
              <Carousel
                style={{ height: '375px' }}
                onChange={(index) => setCurrent(index)}
              >
                {swiperList.map((item, index) => (
                  <Image
                    key={index}
                    src={item}
                    fit="cover"
                    width="100%"
                    height="375px"
                    onClick={() => setShowImagePreview(true)}
                  />
                ))}
              </Carousel>
              <View className="imgnum">
                <Text>{`${current + 1}/${swiperList.length}`}</Text>
              </View>
            </View>
          )}

          {/* 操作按钮 */}
          {userId == userInfo.userId && (
            <View className="opera">
              <Text onClick={handleEdit} className="opera-text">编辑</Text>
              <Text onClick={handleTop} className="opera-text">
                {dynamic.isTop == '1' ? '取顶' : '置顶'}
              </Text>
              <Text onClick={handleDelete} className="opera-text">删除</Text>
              <Text onClick={handleDown} className="opera-text">下架</Text>
              <Text onClick={handleRefresh} className="opera-text">刷新</Text>
            </View>
          )}

          {/* 商品信息 */}
          <View className="dynamicSign">
            {dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== '' && (
              <View className="dynamicSign-price">
                <Text className="dynamicSign-price-title">售价</Text>
                <Text className="dynamicSign-price-text">
                  ￥{Number(dynamic.price)}
                </Text>
              </View>
            )}
            <View className="dynamicSign-desc">
              <Text className="dynamicSign-desc-text">
                {dynamic.content?.replace(/(\r\n|\n|\r)/gm, '')}
              </Text>
              <View className="dynamicSign-desc-collect" onClick={handleCollect}>
                <Image
                  className="dynamicSign-desc-collect-img"
                  bottomOverlap={null}
                  src={dynamic.isCollect === 1 ? 
                    require('@/assets/images/common/trend_collect_p.png') :
                    require('@/assets/images/common/trend_collect_n.png')
                  }
                />
                <Text className="dynamicSign-desc-collect-text">
                  {dynamic.isCollect === 1 ? '已收藏' : '收藏'}
                </Text>
              </View>
            </View>
          </View>

          {/* 商家信息 */}
          <View className="myInfo">
            <View className="myInfo-head">
              <Avatar
                className="myInfo-head-image"
                src={merchantUserInfo.avatar || defaultHeadImg}
              />
              <View className="myInfo-head-name">
                <Text>{merchantUserInfo.nickname}</Text>
              </View>
              <View className="myInfo-head-entry" onClick={getIntoMerchantHomePage}>
                <Text>店铺主页</Text>
              </View>
            </View>
            <View className="myInfo-more">
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">上新</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.newNumbers}
                </Text>
              </View>
              <View className="myInfo-more-divider" />
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">总数</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.total}
                </Text>
              </View>
            </View>
          </View>

          {/* 图文详情 */}
          {(dynamic.pictures !== '' || dynamic.content !== '') && (
            <>
              <View className="dlistImage_title">—— 图文详情 ——</View>
              <View className="dlistImage_desc">
                <Text selectable>{dynamic.dynamic_title}</Text>
              </View>
              {dynamic.pictures !== '' && swiperList.map((item, index) => (
                <View key={index}>
                  <Image 
                    className="dlistImage"
                    src={item}
                    // mode="widthFix"
                  />
                </View>
              ))}
            </>
          )}

          {/* 底部操作栏 */}
          {!isCarShow && (
            <View className="footer">
              <Button 
                className="footer-share"
                onClick={() => {
                  if(userId == userInfo.userId) {
                    //分享

                  } else {
                    //转存
                    Taro.setStorageSync('releaseDynamicList', dynamic);
                    Taro.navigateTo({
                      url: `/pages/releaseDynamic/index?type=4`
                    });
                  }
                }}
              >
                {userId == userInfo.userId ? '一键分享' : '一键转存'}
              </Button>
              {dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== '' && Number(dynamic.price) > 0 && (
                <View className="footer-car"
                  onClick={handleAddCar}
                >
                  <Image
                    className="footer-car-img"
                    bottomOverlap={null}
                    src={require('@/assets/images/common/good_car_icon.png')}
                  />
                </View>
              )}
            </View>
          )}

          {/* 购物车入口 */}
          <View className="car" onClick={() => {Taro.navigateTo({ url: '/pages/cart/index' });}}>
            <Image
              className="car-img"
              bottomOverlap={null}
              src={require('@/assets/images/common/car_icon.png')}
            />
            {carListNum > 0 && (
              <Text className="car-text">{carListNum}</Text>
            )}
          </View>

          {/* 购物车组件 */}
          {/* <GoodCar ref={goodCarRef} /> */}
        </>
      )}
      {/* 2. 使用声明式的 ActionSheet 组件 */}
      <ActionSheet
        className='demo-action-sheet'
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        close={() => setActionSheetVisible(false)}
        maskClosable
        subTitle={(
          <View className="move-item-my" >
            <Image radius={10} className="move-img" src={swiperList[0] || ''} />
            <View className="move-info-my-s">
                <Text className="move-title-text-my" >{dynamic.content || dynamic.dynamic_title || ''}</Text>
                <Text className="move-price">¥{dynamic.price || 0}</Text>
            </View>
          </View>
        )}
        items={[
          {
            content: (
              <View className="sku-container">
                {/* 颜色Tab切换 */}
                {productColorArr.length > 0 && (
                  <View className="color-tabs">
                    {/* Tab头部 */}
                    <View className="color-tab-header">
                      {productColorArr.map((color, colorIndex) => (
                        <View
                          key={colorIndex}
                          className={`color-tab-item ${activeColorIndex === colorIndex ? 'active' : ''}`}
                          onClick={() => setActiveColorIndex(colorIndex)}
                        >
                          <Text className="color-tab-text">{color}</Text>
                        </View>
                      ))}
                    </View>

                    {/* 批量操作区域 - 放在tab下面 */}
                    <Cell
                      label="批量"
                      style={{height:'40px', borderBottom: 'none'}}
                      className="no-border-cell"
                    >
                      <View className="batch-stepper-wrapper">
                        <Stepper
                          style={{marginLeft:'150px'}}
                          min={-100}
                          max={100}
                          step={1}
                          value={0}
                          onChange={(val) => {
                            // 根据变化方向调用批量操作
                            if (val !== null && typeof val === 'number') {
                              if (val > batchPrevValue) {
                                handleBatchOperation('add');
                              } else if (val < batchPrevValue) {
                                handleBatchOperation('subtract');
                              }
                              setBatchPrevValue(val);
                            }
                          }}
                        />
                      </View>
                    </Cell>

                    {/* Tab内容 */}
                    <View className="color-tab-content">
                      <View className="spec-list">
                        {productSpecificationsArr.map((spec, specIndex) => (
                          <Cell
                            label={spec}
                            style={{height:'40px', borderBottom: 'none'}}
                            className="no-border-cell"
                            key={`${productColorArr[activeColorIndex]}-${spec}-${specIndex}`}
                          >
                            <Stepper
                              key={`stepper-${activeColorIndex}-${specIndex}`}
                              style={{marginLeft:'150px'}}
                              min={0}
                              max={100}
                              step={1}
                              defaultValue={0}
                              value={stepperValues[activeColorIndex]?.[specIndex] ?? 0}
                              onChange={val => {
                                if (val !== null && typeof val === 'number') {
                                  const newValues = [...stepperValues];
                                  if (!newValues[activeColorIndex]) {
                                    newValues[activeColorIndex] = Array(productSpecificationsArr.length).fill(0);
                                  }
                                  newValues[activeColorIndex][specIndex] = val;
                                  setStepperValues(newValues);
                                }
                              }}
                            />
                          </Cell>
                        ))}
                      </View>
                    </View>
                  </View>
                )}

                {/* 如果没有颜色，只显示规格 */}
                {productColorArr.length === 0 && (
                  <View className="spec-list">
                    {productSpecificationsArr.map((spec, specIndex) => (
                      <Cell
                        label={spec}
                        style={{height:'40px', borderBottom: 'none'}}
                        className="no-border-cell"
                        key={`spec-${specIndex}`}
                      >
                        <Stepper
                          key={`stepper-0-${specIndex}`}
                          style={{marginLeft:'150px'}}
                          min={0}
                          max={100}
                          step={1}
                          defaultValue={0}
                          value={stepperValues[0]?.[specIndex] ?? 0}
                          onChange={val => {
                            if (val !== null && typeof val === 'number') {
                              const newValues = [...stepperValues];
                              if (!newValues[0]) {
                                newValues[0] = Array(productSpecificationsArr.length).fill(0);
                              }
                              newValues[0][specIndex] = val;
                              setStepperValues(newValues);
                            }
                          }}
                        />
                      </Cell>
                    ))}
                  </View>
                )}
              </View>
            ),
            style: {height:'100%'},
            onClick:() => true
          },
          {
            content: (
              <Textarea
                value={remark}
                onChange={(_, value) => setRemark(value)}
                onErrStatusChange={hasError => console.log('hasError', hasError)}
                autosize
                placeholder="可备注颜色，尺码..."
                border="none"
              />
            ),
            style: {height:'100%'},
            onClick:() => true
          },
          { 
            content: (
              <View style={{width:'100%',padding:'0 15px 15px 0'}}>
                <View style={{display:'flex',justifyContent:'flex-end',alignItems:'center',marginBottom: '12px'}}>
                  {/* 统计总件数 */}
                  <Text style={{fontSize:16,color:'#222',fontWeight:500}}>共<Text style={{color:'#e35848'}}>{getTotalQuantityAndPrice().totalQuantity}</Text>件 <Text style={{color:'#e35848'}}>￥{getTotalQuantityAndPrice().totalPrice}</Text></Text>
                </View>
                <View style={{display:'flex',gap:'16px',padding:'0 16px 16px 16px',marginTop: '30px'}}>
                  <Button
                    style={{
                      flex:1,
                      height:44,
                      background: getTotalQuantityAndPrice().totalQuantity > 0 ? '#e6f3ff' : '#f7f8fa',
                      color: getTotalQuantityAndPrice().totalQuantity > 0 ? '#1890ff' : '#bcbcbc',
                      borderRadius:8,
                      border:'none',
                      fontSize:14
                    }}
                    onClick={(e) => {
                      // 阻止事件冒泡，防止弹框关闭
                      e.stopPropagation();
                      // 弹框内的加购物车按钮
                      if (getTotalQuantityAndPrice().totalQuantity > 0) {
                        addCartData();
                        // 成功后关闭弹框
                        setActionSheetVisible(false);
                      } else {
                        Toast.error({ content: '请选择商品规格和数量' });
                        // 不关闭弹框，让用户继续选择
                      }
                    }}
                  >加入购物车</Button>
                  <Button
                    style={{
                      flex:1,
                      height:44,
                      background: getTotalQuantityAndPrice().totalQuantity > 0 ? '#1890ff' : '#93bfff',
                      color: '#fff',
                      borderRadius:8,
                      border:'none',
                      fontSize:14
                    }}
                    onClick={(e) => {
                      // 阻止事件冒泡，防止弹框关闭
                      e.stopPropagation();
                      // 弹框内的立即购买按钮
                      if (getTotalQuantityAndPrice().totalQuantity > 0) {
                        // Taro.navigateTo({ url: '/pages/cart/index' });
                          Taro.navigateTo({
                            url: '/pages/order/pay/index'
                          });
                          // Taro.setStorageSync('selectedItems', selectedItems || []);
                        // 成功后关闭弹框
                        setActionSheetVisible(false);
                      } else {
                        Toast.error({ content: '请选择商品规格和数量' });
                        // 不关闭弹框，让用户继续选择
                      }
                    }}
                  >立即购买</Button>
                </View>
              </View>
            ), 
            style: {height:120,marginTop:-30},
          }
        ]}
      />
      <ImagePreviewList
  show={showImagePreview}
  images={swiperList}
  initialIndex={current}
  dynamic={dynamic}
  onClose={() => setShowImagePreview(false)}
  showCar={true}
  showBottom={true}
  userInfo={userInfo}
/>
    </View>
  );
}
