import { View, Text, Image } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";
import { useState, useEffect, useCallback } from "react";
import { 
  Cell, 
  Toast, 
  Loading,
  PullRefresh,
  Avatar
} from "@arco-design/mobile-react";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// API
import { getFansList } from "@/utils/api/common/common_user";

// Debug系统
import {
  createDebugger,
  createDebugConfig,
  DebugPanel
} from "@/pages/debug";

// 日期格式化工具函数
const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const dayBeforeYesterday = new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000);
  
  // 判断是否是今天、昨天、前天
  if (date.toDateString() === today.toDateString()) {
    return "今天";
  } else if (date.toDateString() === yesterday.toDateString()) {
    return "昨天";
  } else if (date.toDateString() === dayBeforeYesterday.toDateString()) {
    return "前天";
  }
  
  // 判断是否是当年
  if (date.getFullYear() === now.getFullYear()) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}/${day}`;
  } else {
    // 往年格式
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}/${month}/${day}`;
  }
};

// 粉丝项数据接口
interface FanItem {
  id: number;
  userId: number;
  followId: number;
  followNickname: string;
  followAvatar: string;
  total: number | null;
  newNumbers: number | null;
  createTime: number; // 关注时间（毫秒时间戳）
}

// 粉丝列表响应接口
interface FansListResponse {
  code: number;
  data: {
    list: FanItem[];
    total: number;
  };
  msg: string;
}

// 页面状态接口
interface FansPageState {
  loading: boolean;
  refreshing: boolean;
  data: FanItem[];
  hasMore: boolean;
  page: number;
  pageSize: number;
  error: string | null;
}

// Debug配置
const debugConfig = createDebugConfig('粉丝页面', {
  scenarios: {
    mock_data: '模拟数据',
    paginated_data: '分页数据',
    empty_state: '空状态',
    error_state: '错误状态',
    loading_state: '加载状态'
  }
});

const fanDebugger = createDebugger(debugConfig);

export default function FansPage() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<FansPageState>({
    loading: false,
    refreshing: false,
    data: [],
    hasMore: true,
    page: 1,
    pageSize: 20,
    error: null
  });

  // Debug状态
  const [debugScenario, setDebugScenario] = useState('');

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState(prev => ({ ...prev, loading: true, error: null }));
    try {
      await loadFansList(1, true);
    } catch (error) {
      console.error('粉丝页面初始化失败:', error);
      Toast.error('页面加载失败');
    }
  };

  // ==================== 数据加载 ====================
  const loadFansList = useCallback(async (page: number = 1, isRefresh: boolean = false) => {
    // Debug模式下的模拟数据处理
    if (debugScenario === 'mock_data') {
      return loadMockData();
    }
    
    if (debugScenario === 'paginated_data') {
      setPageState(prev => ({
        ...prev,
        loading: true,
        refreshing: isRefresh
      }));
      return loadPaginatedData(page, isRefresh);
    }
    
    if (debugScenario === 'empty_state') {
      setPageState(prev => ({
        ...prev,
        loading: false,
        refreshing: false,
        data: [],
        hasMore: false
      }));
      return;
    }

    if (debugScenario === 'error_state') {
      setPageState(prev => ({
        ...prev,
        loading: false,
        refreshing: false,
        error: '模拟网络错误'
      }));
      return;
    }

    if (debugScenario === 'loading_state') {
      // 保持loading状态用于测试
      return;
    }

    if (pageState.loading && !isRefresh) return;

    // 获取用户ID
    const userInfo = Taro.getStorageSync('userInfo');
    const userId = userInfo?.userId || userInfo?.userInfo?.userId;
    
    if (!userId) {
      setPageState(prev => ({
        ...prev,
        loading: false,
        refreshing: false,
        error: '用户信息获取失败，请重新登录'
      }));
      Toast.error('用户信息获取失败，请重新登录');
      return;
    }

    setPageState(prev => ({
      ...prev,
      loading: true,
      refreshing: isRefresh,
      error: null
    }));

    try {
      const response = await getFansList({
        page,
        pageSize: pageState.pageSize,
        userId
      }) as FansListResponse;

      if (response.code === 0) {
        const newData = response.data.list || [];
        setPageState(prev => ({
          ...prev,
          data: isRefresh ? newData : [...prev.data, ...newData],
          hasMore: newData.length === prev.pageSize,
          page: isRefresh ? 1 : page,
          loading: false,
          refreshing: false,
          error: null
        }));
      } else {
        throw new Error(response.msg || '获取粉丝列表失败');
      }
    } catch (error) {
      console.error('获取粉丝列表失败:', error);
      const errorMessage = error instanceof Error ? error.message : '网络错误，请重试';
      
      setPageState(prev => ({
        ...prev,
        loading: false,
        refreshing: false,
        error: errorMessage
      }));
      
      Toast.error(errorMessage);
    }
  }, [pageState.loading, pageState.pageSize, debugScenario]);

  // ==================== 模拟数据 ====================
  const loadMockData = () => {
    const now = Date.now();
    const mockData: FanItem[] = [
      {
        id: 1,
        userId: 17033,
        followId: 23909,
        followNickname: "孔阳",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/55653774308d6602ccc86319f76f306c.webp",
        total: null,
        newNumbers: null,
        createTime: now - 24 * 60 * 60 * 1000 // 昨天
      },
      {
        id: 2,
        userId: 17034,
        followId: 23910,
        followNickname: "尹雪瑶",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/54338f69101147e55dab193c03c41d9d.png",
        total: null,
        newNumbers: null,
        createTime: now - 2 * 24 * 60 * 60 * 1000 // 前天
      },
      {
        id: 3,
        userId: 17035,
        followId: 23911,
        followNickname: "葛昕蕊",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/f2e9ae6910ea5115ebbb08bb44a390d0.webp",
        total: null,
        newNumbers: null,
        createTime: now // 今天
      },
      {
        id: 4,
        userId: 17036,
        followId: 23912,
        followNickname: "水添池",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/afc9d6c70427882a0b9a85bc9eae4fd4.webp",
        total: null,
        newNumbers: null,
        createTime: now - 30 * 24 * 60 * 60 * 1000 // 30天前（当年）
      },
      {
        id: 5,
        userId: 17037,
        followId: 23913,
        followNickname: "周雪",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/1874adaf360fec74eac4b475782c4998.webp",
        total: null,
        newNumbers: null,
        createTime: now - 7 * 24 * 60 * 60 * 1000 // 7天前（当年）
      },
      {
        id: 6,
        userId: 17038,
        followId: 23914,
        followNickname: "花佳怡",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/969ed86261da15158f95e9353a7bb286.webp",
        total: null,
        newNumbers: null,
        createTime: now - 365 * 24 * 60 * 60 * 1000 // 去年
      }
    ];

    setPageState(prev => ({
      ...prev,
      loading: false,
      refreshing: false,
      data: mockData,
      hasMore: false,
      error: null
    }));
  };

  // ==================== 分页模拟数据 ====================
  const loadPaginatedData = (page: number = 1, isRefresh: boolean = false) => {
    const now = Date.now();
    
    // 生成大量模拟数据
    const generateMockFan = (id: number): FanItem => {
      const names = [
        "孔阳", "尹雪瑶", "葛昕蕊", "水添池", "周雪", "花佳怡", 
        "李明", "王小红", "张伟", "刘芳", "陈静", "杨帆",
        "赵丽", "钱进", "孙悦", "李娜", "周杰", "吴彦祖",
        "郑爽", "王菲", "刘德华", "张学友", "黎明", "郭富城",
        "梁朝伟", "刘嘉玲", "张曼玉", "王祖贤", "林青霞", "邱淑贞"
      ];
      
      const avatars = [
        "https://image-resource.mastergo.com/92419737293347/152770503910353/55653774308d6602ccc86319f76f306c.webp",
        "https://image-resource.mastergo.com/92419737293347/152770503910353/54338f69101147e55dab193c03c41d9d.png",
        "https://image-resource.mastergo.com/92419737293347/152770503910353/f2e9ae6910ea5115ebbb08bb44a390d0.webp",
        "https://image-resource.mastergo.com/92419737293347/152770503910353/afc9d6c70427882a0b9a85bc9eae4fd4.webp",
        "https://image-resource.mastergo.com/92419737293347/152770503910353/1874adaf360fec74eac4b475782c4998.webp",
        "https://image-resource.mastergo.com/92419737293347/152770503910353/969ed86261da15158f95e9353a7bb286.webp"
      ];

      // 随机生成关注时间
      const timeRanges = [
        now, // 今天
        now - 24 * 60 * 60 * 1000, // 昨天
        now - 2 * 24 * 60 * 60 * 1000, // 前天
        now - Math.random() * 30 * 24 * 60 * 60 * 1000, // 30天内
        now - Math.random() * 365 * 24 * 60 * 60 * 1000, // 一年内
        now - (365 + Math.random() * 365) * 24 * 60 * 60 * 1000 // 往年
      ];

      return {
        id,
        userId: 17000 + id,
        followId: 23900 + id,
        followNickname: names[id % names.length] + (id > names.length ? ` ${Math.floor(id / names.length)}` : ''),
        followAvatar: avatars[id % avatars.length],
        total: null,
        newNumbers: null,
        createTime: Math.floor(timeRanges[id % timeRanges.length])
      };
    };

    // 模拟网络延迟
    setTimeout(() => {
      const pageSize = pageState.pageSize;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      
      // 生成当前页数据
      const currentPageData: FanItem[] = [];
      for (let i = startIndex; i < endIndex; i++) {
        currentPageData.push(generateMockFan(i + 1));
      }

      // 模拟总共100条数据，分页加载
      const totalItems = 100;
      const hasMore = endIndex < totalItems;

      setPageState(prev => ({
        ...prev,
        data: isRefresh ? currentPageData : [...prev.data, ...currentPageData],
        hasMore,
        page: page,
        loading: false,
        refreshing: false,
        error: null
      }));

      fanDebugger.debugLog(`分页加载完成: 第${page}页, 加载${currentPageData.length}条, 还有更多: ${hasMore}`);
    }, 800); // 模拟800ms网络延迟
  };

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    await loadFansList(1, true);
  }, [loadFansList]);

  // 加载更多
  const handleLoadMore = useCallback(() => {
    if (pageState.hasMore && !pageState.loading) {
      loadFansList(pageState.page + 1);
    }
  }, [pageState.hasMore, pageState.loading, pageState.page, loadFansList]);

  // 粉丝项点击
  const handleFanItemClick = (fan: FanItem) => {
    // 可以跳转到用户详情页面
    Toast.info(`点击了粉丝: ${fan.followNickname}`);
  };

  // Debug场景切换
  const handleDebugScenario = (scenario: string) => {
    setDebugScenario(scenario);
    fanDebugger.debugLog('切换调试场景', { scenario });
    
    // 重新加载数据以应用新场景
    if (scenario !== 'loading_state') {
      loadFansList(1, true);
    }
  };

  // ==================== 渲染函数 ====================
  // 渲染粉丝列表项
  const renderFanItem = (fan: FanItem) => {
    const formatDate = (timestamp: number) => {
      const date = new Date(timestamp);
      return `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`;
    };

    // 根据实际业务逻辑，这里简化为显示关注时间
    const statusText = '已添加';
    const dateText = formatDate(fan.createTime);

    return (
      <Cell key={fan.id} className="fan-item" onClick={() => handleFanItemClick(fan)}
          label={
          <View className="fan-item-inner">
            <View className="fan-avatar">
              <Avatar
                src={fan.followAvatar}
                size="small"
                // style={{ borderRadius: '50%' }}
              />
            </View>

            {/* 中间内容 */}
            <View className="fan-content">
              <Text className="fan-nickname">{fan.followNickname || '未知用户'}</Text>
            </View>
          </View> 
        }
        children={
          <View className="fan-info">
            <Text className="fan-status">{statusText}</Text>
            {dateText && (
              <Text className="fan-date">{dateText}</Text>
            )}
          </View>
          }
        >
      </Cell>
    );
  };

  // ==================== 主渲染 ====================
  return (
    <View className="fansPage">
      <YkNavBar title="粉丝" onClickLeft={handleBack} />
      
      <PullRefresh onRefresh={handleRefresh}>
        <View className="content">
          {/* 错误状态 */}
          {pageState.error && !pageState.loading && (
            <View className="error-container">
              <View className="error-message">
                <Text className="error-text">{pageState.error}</Text>
              </View>
              <View className="retry-button">
                <Cell
                  label="重试"
                  showArrow
                  onClick={() => loadFansList(1, true)}
                />
              </View>
            </View>
          )}

          {/* 空状态 */}
          {!pageState.error && pageState.data.length === 0 && !pageState.loading && (
            <View className="empty-container">
              <Text className="empty-text">暂无粉丝数据</Text>
            </View>
          )}

          {/* 粉丝列表 */}
          {!pageState.error && pageState.data.length > 0 && (
            <View className="fans-list">
              <Cell.Group className="list-container" bordered={false}>
                {pageState.data.map(renderFanItem)}
                
                {/* 加载更多触发区域 */}
                {pageState.hasMore && !pageState.loading && (
                  <View 
                    className="load-more-trigger"
                    onClick={handleLoadMore}
                    style={{ 
                      padding: '20px',
                      textAlign: 'center',
                      color: '#999',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}
                  >
                    <Text>点击加载更多</Text>
                  </View>
                )}
              </Cell.Group>
            </View>
          )}

          {/* 加载更多状态 */}
          {pageState.loading && pageState.data.length > 0 && (
            <View className="loading-more">
              <Loading />
              <Text className="loading-text">加载中...</Text>
            </View>
          )}

          {/* 没有更多数据 */}
          {!pageState.hasMore && pageState.data.length > 0 && !pageState.loading && (
            <View className="no-more">
              <Text className="no-more-text">没有更多数据了</Text>
            </View>
          )}

          {/* 初始加载状态 */}
          {pageState.loading && pageState.data.length === 0 && (
            <View className="initial-loading">
              <Loading />
            </View>
          )}
        </View>
      </PullRefresh>

      {/* Debug面板 */}
      {process.env.NODE_ENV === 'development' && (
        <DebugPanel
          scenario={debugScenario}
          config={debugConfig}
          onLoadMockData={handleDebugScenario}
          statusInfo={{
            '数据长度': pageState.data.length,
            '加载状态': pageState.loading ? '加载中' : '已完成',
            '当前页码': pageState.page,
            '每页条数': pageState.pageSize,
            '是否还有更多': pageState.hasMore ? '是' : '否',
            '调试场景': debugScenario || '无',
            '错误信息': pageState.error || '无'
          }}
          position="bottom-right"
        />
      )}
    </View>
  );
} 