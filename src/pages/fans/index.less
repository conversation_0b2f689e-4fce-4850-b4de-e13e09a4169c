// 导入Arco Design变量以保持风格统一
@import '@arco-design/mobile-react/style/mixin.less';
@import "@/utils/css/variables.less";

// 页面根容器样式
[id^="/pages/fans/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.fansPage {
  // 页面背景 - 填充 Fill/填充fill-1: #F7F8FA
  background-color: #F7F8FA;
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
  min-height: 100vh;
  
  * {
    font-family: 'PingFang SC', sans-serif;
  }

  .content {
    padding: 0;
    min-height: calc(100vh - 84px); // 减去导航栏高度
  }

  // 粉丝列表容器
  .fans-list {
    .list-container {
      // 容器背景 - 填充 Fill/card 卡片-背景色: #F7F8FA  
      background-color: #F7F8FA;
      background-color: @background-color !important;
      .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
    }
  }

  // 粉丝项样式
  .fan-item {
    margin: 0;
    padding: 12px 16px;
    // 项目背景 - 填充 Fill/Container 容器背景色: #FFFFFF
    background-color: #FFFFFF;
    background-color: @background-color !important;
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
    // margin-bottom: 0.5px;
    
    &:active {
      opacity: 0.8;
    }

    .fan-item-inner {
      display: flex;
      align-items: center;
      gap: 8px;
      // padding: 12px 16px;
      // 底部分割线 - 线条 Line/线条line-1-基础: #E5E6EB
      // border-bottom: 0.5px solid #E5E6EB;
      // border-bottom-color: @line-color !important;
      // .use-dark-mode-query({
      //   border-bottom-color: @dark-line-color !important;
      // });
      // min-height: 56px;
      
      // &:last-child {
      //   border-bottom: none;
      // }
    }

    // 左侧头像
    .fan-avatar {
      width: 32px;
      height: 32px;
      margin-right: 8px;
      flex-shrink: 1;
    }

    // 中间内容区
    .fan-content {
      flex: 1;
      margin-left: 8px;
      margin-right: 8px;
      
      .fan-nickname {
        // 文字颜色 - 文字 Text/文字-5-基础 Grey 10: #1D2129
        font-size: 16px;
        font-weight: 500;
        color: #1D2129;
        color: @font-color !important;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        font-family: 'PingFangSC-Medium', sans-serif;
        line-height: 1.4;
      }
    }

    // 右侧信息区
    .fan-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      gap: 8px;
      min-width: 48px;

      .fan-status {
        // 状态文字颜色 - 文字 Text/文字-5-基础 Grey 10: #1D2129
        font-size: 16px;
        font-weight: 500;
        color: #1D2129;
        color: @font-color !important;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        font-family: 'PingFangSC-Medium', sans-serif;
        text-align: right;
      }

      .fan-date {
        // 辅助信息颜色 - 文字 Text/文字-3-附加信息-Grey 6: #86909C
        font-size: 14px;
        color: #86909C;
        color: @sub-info-font-color !important;
        .use-dark-mode-query({
          color: @dark-sub-info-font-color !important;
        });
        font-family: 'PingFangSC-Regular', sans-serif;
        line-height: 1.4;
        text-align: right;
      }
    }
  }

  // 错误状态容器
  .error-container {
    padding: 60px 20px;
    text-align: center;

    .error-message {
      margin-bottom: 20px;
      
      .error-text {
        font-size: 14px;
        color: @danger-color;
        .use-dark-mode-query({
          color: @dark-danger-color !important;
        });
        line-height: 1.4;
      }
    }

    .retry-button {
      margin-top: 20px;
      padding: 0 40px;
    }
  }

  // 空状态容器
  .empty-container {
    padding: 80px 20px;
    text-align: center;

    .empty-text {
      font-size: 14px;
      color: @sub-info-font-color;
      .use-dark-mode-query({
        color: @dark-sub-info-font-color !important;
      });
    }
  }

  // 加载更多样式
  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    gap: 8px;

    .loading-text {
      font-size: 14px;
      color: @sub-font-color;
      .use-dark-mode-query({
        color: @dark-sub-font-color !important;
      });
    }
  }

  // 没有更多数据样式
  .no-more {
    padding: 20px;
    text-align: center;

    .no-more-text {
      font-size: 14px;
      color: @sub-info-font-color;
      .use-dark-mode-query({
        color: @dark-sub-info-font-color !important;
      });
    }
  }

  // 初始加载样式
  .initial-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;
  }

  // 空状态样式优化
  .arco-empty {
    padding: 80px 20px;
  }

  // 加载更多触发区域
  .load-more-trigger {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #E5E6EB; // 线条 Line/线条line-1-基础
    .use-dark-mode-query({
      border-top-color: #444 !important;
    });
    margin-top: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #F7F8FA; // 填充 Fill/填充fill-1
      .use-dark-mode-query({
        background-color: #2A2D33 !important;
      });
    }

    &:active {
      background-color: #F2F3F5; // 填充 Fill/填充fill-2
      .use-dark-mode-query({
        background-color: #232529 !important;
      });
    }

    text {
      font-size: 14px;
      color: #165DFF; // 主色 Brand Color/主色-6-基础
      .use-dark-mode-query({
        color: #4080FF !important;
      });
    }
  }
} 