// fetch-post-sse-parser.js
// A POST-capable SSE client that parses Server-Sent Events protocol
// Usage: const ps = new PostEventSource(url, { method: 'POST', body: JSON.stringify({foo:1}), headers: {...} });
// ps.addEventListener('message', e => {});
// ps.addEventListener('customEvent', e => {});
// ps.close();

export default class PostEventSource {
  constructor(url, options = {}) {
    this.url = url;
    this.options = options; // { method, headers, body, keepAlive }

    // listeners: { eventName: [fn, ...] }
    this.listeners = new Map();
    this.lastEventId = null;
    this.retry = 3000; // default retry ms
    this._closed = false;
    this._connecting = false;
    this._abortController = null;

    // start connection automatically
    this.connect();
  }

  addEventListener(event, fn) {
    if (!this.listeners.has(event)) this.listeners.set(event, []);
    this.listeners.get(event).push(fn);
  }

  removeEventListener(event, fn) {
    if (!this.listeners.has(event)) return;
    const arr = this.listeners.get(event).filter(x => x !== fn);
    if (arr.length) this.listeners.set(event, arr); else this.listeners.delete(event);
  }

  dispatchEventObject(obj) {
    const eventName = obj.event || 'message';
    const listeners = this.listeners.get(eventName) || [];
    const messageEvent = {
      data: obj.data == null ? '' : obj.data,
      lastEventId: obj.id || this.lastEventId || '',
      origin: this.url,
      type: eventName,
      // convenience: raw object
      _raw: obj,
    };

    // update lastEventId if provided
    if (obj.id) this.lastEventId = obj.id;

    // call specific listeners
    listeners.forEach(fn => {
      try { fn.call(null, messageEvent); } catch (e) { console.error('listener error', e); }
    });

    // call 'message' listeners also for default
    if (eventName !== 'message') {
      const msgListeners = this.listeners.get('message') || [];
      msgListeners.forEach(fn => {
        try { fn.call(null, messageEvent); } catch (e) { console.error('listener error', e); }
      });
    }
  }

  async connect() {
    if (this._closed) return;
    if (this._connecting) return;
    this._connecting = true;

    const headers = Object.assign({}, this.options.headers || {});
    // Accept text/event-stream and indicate we accept streamed data
    headers['Accept'] = headers['Accept'] || 'text/event-stream';
    if (this.lastEventId) headers['Last-Event-ID'] = this.lastEventId;

    this._abortController = new AbortController();

    const fetchOpts = {
      method: this.options.method || 'GET',
      headers,
      signal: this._abortController.signal,
    };

    // allow body as function to re-generate on reconnect
    if (this.options.body !== undefined) {
      fetchOpts.body = typeof this.options.body === 'function' ? this.options.body() : this.options.body;
    }

    try {
      const res = await fetch(this.url, fetchOpts);

      if (!res.ok) {
        const err = new Error('HTTP error ' + res.status);
        this._handleError(err);
        return;
      }

      const contentType = res.headers.get('content-type') || '';
      // Not strictly required, but warn if not SSE
      if (!contentType.includes('text/event-stream')) {
        console.warn('Response Content-Type is not text/event-stream:', contentType);
      }

      const reader = res.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';

      // parser state
      let fieldEvent = undefined;
      let fieldData = '';
      let fieldId = undefined;
      let fieldRetry = undefined;

      const flushEvent = () => {
        if (fieldData === '' && fieldEvent === undefined && fieldId === undefined && fieldRetry === undefined) return;
        // trim trailing newline from data
        let data = fieldData.replace(/\n$/, '');
        const obj = {
          event: fieldEvent,
          data,
          id: fieldId,
        };
        if (fieldRetry !== undefined) {
          const n = parseInt(fieldRetry, 10);
          if (!Number.isNaN(n)) this.retry = n;
        }

        // dispatch
        this.dispatchEventObject(obj);

        // reset
        fieldEvent = undefined;
        fieldData = '';
        fieldId = undefined;
        fieldRetry = undefined;
      };

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });

        // split lines, keep trailing partial line in buffer
        const lines = buffer.split(/\r?\n/);
        buffer = lines.pop() || '';

        for (let line of lines) {
          if (line === '') {
            // dispatch event
            flushEvent();
            continue;
          }
          if (line[0] === ':') {
            // comment, ignore
            continue;
          }

          const idx = line.indexOf(':');
          let field, valuePart;
          if (idx === -1) {
            field = line;
            valuePart = '';
          } else {
            field = line.slice(0, idx);
            valuePart = line.slice(idx + 1);
            if (valuePart.startsWith(' ')) valuePart = valuePart.slice(1);
          }

          switch (field) {
            case 'event':
              fieldEvent = valuePart;
              break;
            case 'data':
              fieldData += valuePart + '\n';
              break;
            case 'id':
              fieldId = valuePart;
              break;
            case 'retry':
              fieldRetry = valuePart;
              break;
            default:
              // unknown field, ignore or could store
              break;
          }
        }
      }

      // stream ended - flush any pending event
      if (buffer.length) {
        // process remaining buffer as if terminated by newline
        const remainingLines = buffer.split(/\r?\n/);
        for (let line of remainingLines) {
          if (line === '') { flushEvent(); continue; }
          if (line[0] === ':') continue;
          const idx = line.indexOf(':');
          let field, valuePart;
          if (idx === -1) { field = line; valuePart = ''; } else {
            field = line.slice(0, idx);
            valuePart = line.slice(idx + 1);
            if (valuePart.startsWith(' ')) valuePart = valuePart.slice(1);
          }
          switch (field) {
            case 'event': fieldEvent = valuePart; break;
            case 'data': fieldData += valuePart + '\n'; break;
            case 'id': fieldId = valuePart; break;
            case 'retry': fieldRetry = valuePart; break;
          }
        }
        flushEvent();
      }

      // if ended naturally and not closed by user, reconnect
      if (!this._closed) {
        // small delay before reconnect
        await this._delay(this.retry);
        this._connecting = false;
        this.connect();
      }

    } catch (err) {
      if (this._closed) return;
      this._handleError(err);
    } finally {
      this._connecting = false;
    }
  }

  _handleError(err) {
    console.error('PostEventSource error', err);
    const listeners = this.listeners.get('error') || [];
    listeners.forEach(fn => {
      try { fn.call(null, err); } catch (e) { console.error(e); }
    });

    if (this._closed) return;
    // reconnect after retry
    setTimeout(() => {
      if (!this._closed) this.connect();
    }, this.retry);
  }

  _delay(ms) { return new Promise(res => setTimeout(res, ms)); }

  close() {
    this._closed = true;
    try {
      if (this._abortController) this._abortController.abort();
    } catch (e) {}
  }
}

// AI 聊天 SSE 客户端工厂函数
export function createAiChatSSEClient(options = {}) {
  const {
    baseUrl = 'http://123.6.102.143:38080',
    tenantId = '1',
    authorization = 'Bearer ee12a8beaca5445ab4740133cd1b6016',
    conversationId,
    content,
    useContext = true
  } = options;

  const url = `${baseUrl}/app-api/ai/chat/message/send-stream`;
  
  const headers = {
    'Content-Type': 'application/json',
    'tenant-id': tenantId,
    'Authorization': authorization,
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive'
  };

  const body = JSON.stringify({
    conversationId,
    content,
    useContext
  });

  return new PostEventSource(url, {
    method: 'POST',
    headers,
    body
  });
}

// --------- Example usage (not part of the class) ----------
/*
import PostEventSource from './fetch-post-sse-parser.js';

const ps = new PostEventSource('/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: () => JSON.stringify({ userId: 123 }) // body can be function for reconnects
});

ps.addEventListener('message', e => {
  console.log('message', e.data);
});

ps.addEventListener('progress', e => {
  console.log('progress event', e.data);
});

ps.addEventListener('error', err => {
  console.error('stream error', err);
});

// to close
// ps.close();
*/

