import PostEventSource, { createAiChatSSEClient } from './fetch-post-sse-parser.js';

// 测试 AI 聊天 SSE 接口
console.log('🚀 开始测试 AI 聊天 SSE 接口...');

const aiChatClient = createAiChatSSEClient({
  conversationId: '1781604279872581810',
  content: '帮我写个c++ hellworld算法',
  useContext: true
});

// 监听消息事件
aiChatClient.addEventListener('message', e => {
  try {
    const data = JSON.parse(e.data);
    console.log('📨 收到消息:', {
      code: data.code,
      msg: data.msg,
      data: data.data
    });
    
    // 如果有接收到的消息内容，打印出来
    if (data.data && data.data.receive && data.data.receive.content) {
      console.log('🤖 AI 回复:', data.data.receive.content);
    }
  } catch (error) {
    console.log('📨 原始消息数据:', e.data);
  }
});

// 监听错误事件
aiChatClient.addEventListener('error', err => {
  console.error('❌ SSE 连接错误:', err);
});

// 监听连接关闭
aiChatClient.addEventListener('close', () => {
  console.log('🔌 SSE 连接已关闭');
});

// 5秒后自动关闭连接（可选）
setTimeout(() => {
  console.log('⏰ 5秒后自动关闭连接...');
  aiChatClient.close();
}, 5000);

// 手动关闭连接的示例
// aiChatClient.close();

console.log('✅ 测试客户端已启动，等待 AI 回复...');
