# AI 聊天 SSE 接口测试工具

这个目录包含了用于测试 AI 聊天 SSE（Server-Sent Events）接口的工具。

## 文件说明

- `fetch-post-sse-parser.js` - SSE 客户端核心实现
- `sse-test-client.js` - 浏览器环境测试客户端
- `test-ai-sse.js` - Node.js 环境测试脚本
- `package.json` - 项目配置文件

## 使用方法

### 1. Node.js 环境测试

```bash
# 进入测试目录
cd arcoxc/src/pages/debug

# 运行测试
node test-ai-sse.js
```

### 2. 浏览器环境测试

在浏览器中引入 `sse-test-client.js` 文件：

```html
<script type="module">
  import './sse-test-client.js';
</script>
```

### 3. 自定义配置

修改 `test-ai-sse.js` 中的 `testConfig` 对象：

```javascript
const testConfig = {
  conversationId: '你的对话ID',
  content: '你想问的问题',
  useContext: true,
  baseUrl: 'http://你的服务器地址:端口',
  tenantId: '你的租户ID',
  authorization: 'Bearer 你的认证令牌'
};
```

## 接口信息

测试的接口：`POST /app-api/ai/chat/message/send-stream`

### 请求头
- `Content-Type: application/json`
- `tenant-id: 1`
- `Authorization: Bearer ee12a8beaca5445ab4740133cd1b6016`
- `Accept: text/event-stream`
- `Cache-Control: no-cache`
- `Connection: keep-alive`

### 请求体
```json
{
  "conversationId": "1781604279872581810",
  "content": "帮我写个c++ hellworld算法",
  "useContext": true
}
```

### 响应格式
```
data:{"code":0,"data":{"send":{"id":2859,"type":"user","content":"帮我写个c++ hellworld算法","segmentIds":[],"segments":null,"createTime":1754963927981},"receive":{"id":2860,"type":"assistant","content":"这","segmentIds":[],"segments":[],"createTime":1754963927983}},"msg":""}
```

## 功能特性

- ✅ 支持 POST 请求的 SSE 客户端
- ✅ 自动重连机制
- ✅ 错误处理
- ✅ 流式数据解析
- ✅ 支持自定义请求头和参数
- ✅ 优雅的连接关闭

## 注意事项

1. 确保 Node.js 版本 >= 16.0.0
2. 确保网络连接正常
3. 确保认证令牌有效
4. 测试完成后会自动关闭连接
5. 可以按 Ctrl+C 手动停止测试

## 故障排除

### 常见错误

1. **CORS 错误**：在浏览器环境中可能遇到跨域问题
2. **认证失败**：检查 Authorization 头是否正确
3. **连接超时**：检查服务器地址和网络连接
4. **解析错误**：检查响应数据格式是否正确

### 调试技巧

1. 查看控制台输出的详细日志
2. 检查网络请求的详细信息
3. 验证请求头和请求体格式
4. 确认服务器端 SSE 实现是否正确

