/**
 * 自定义组件展示页面
 * 展示src/components目录下的所有自定义组件
 */

import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';

import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import {
  createDebugger,
  createDebugConfig,
  DebugPanel
} from './index';

// 导入所有自定义组件
import YkNavBar from '@/components/ykNavBar';
import YkSwitchTabBar from '@/components/ykSwitchTabBar';
import YkImagePicker from '@/components/YkImagePicker';
import YkNoticeBar from '@/components/YkNoticeBar';
import YkCellLabel from '@/components/YkCellLabel';
import BottomPopup from '@/components/BottomPopup';
import FullScreenLoading from '@/components/FullSrceenLoading';
import PageAnimation from '@/components/PageAnimation';
import PermissionPopup from '@/components/PermissionPopup';
import ProtocolMask from '@/components/ProtocolMask';
import YkAreaPicker from '@/components/ykAreaPicker';
import YkDatePicker from '@/components/ykDatePicker';
import YkIndustryPicker from '@/components/ykIndustryPicker';
import * as YkIcons from '@/components/YkIcons';
import { getAllIcons, getIconsInfo } from '@/components/YkIcons';
import Msgx from '@/components/Msgx';

import './test.less';

// 组件展示数据类型
interface ComponentDemoData {
  currentComponent: string;
  componentProps: any;
}

const ComponentTest: React.FC = () => {
  const [demoData, setDemoData] = useState<ComponentDemoData>({
    currentComponent: 'YkNavBar',
    componentProps: {}
  });

  const [componentStates, setComponentStates] = useState({
    showBottomPopup: false,
    showFullScreenLoading: false,
    showPermissionPopup: false,
    showProtocolMask: false,
    selectedImage: null as ImagePickItem | null,
    selectedArea: '',
    selectedDate: '',
    selectedIndustry: '',
    msgxVisible: false,
    msgxDirection: 'topCenter' as any
  });

  // 图标刷新状态
  const [iconRefreshKey, setIconRefreshKey] = useState(0);

  // 创建组件展示调试器配置
  const testDebugger = createDebugger(
    createDebugConfig('自定义组件展示', {
      scenarios: {
        basic: {
          displayName: '基础展示',
          icon: '🏠',
          description: '展示所有组件的基本状态',
          color: 'primary'
        },
        ykNavBar: {
          displayName: 'YkNavBar',
          icon: '📱',
          description: '导航栏组件',
          color: 'primary'
        },
        ykSwitchTabBar: {
          displayName: 'YkSwitchTabBar',
          icon: '🔄',
          description: '切换标签栏组件',
          color: 'primary'
        },
        ykImagePicker: {
          displayName: 'YkImagePicker',
          icon: '📷',
          description: '图片选择器组件',
          color: 'primary'
        },
        ykNoticeBar: {
          displayName: 'YkNoticeBar',
          icon: '📢',
          description: '通知栏组件',
          color: 'warning'
        },
        ykCellLabel: {
          displayName: 'YkCellLabel',
          icon: '🏷️',
          description: '单元格标签组件',
          color: 'primary'
        },
        bottomPopup: {
          displayName: 'BottomPopup',
          icon: '⬆️',
          description: '底部弹窗组件',
          color: 'primary'
        },
        fullScreenLoading: {
          displayName: 'FullScreenLoading',
          icon: '⏳',
          description: '全屏加载组件',
          color: 'primary'
        },
        pageAnimation: {
          displayName: 'PageAnimation',
          icon: '✨',
          description: '页面动画组件',
          color: 'primary'
        },
        permissionPopup: {
          displayName: 'PermissionPopup',
          icon: '🔐',
          description: '权限弹窗组件',
          color: 'warning'
        },
        protocolMask: {
          displayName: 'ProtocolMask',
          icon: '📄',
          description: '协议遮罩组件',
          color: 'primary'
        },
        ykAreaPicker: {
          displayName: 'YkAreaPicker',
          icon: '🌍',
          description: '地区选择器组件',
          color: 'primary'
        },
        ykDatePicker: {
          displayName: 'YkDatePicker',
          icon: '📅',
          description: '日期选择器组件',
          color: 'primary'
        },
        ykIndustryPicker: {
          displayName: 'YkIndustryPicker',
          icon: '🏢',
          description: '行业选择器组件',
          color: 'primary'
        },
        ykIcons: {
          displayName: 'YkIcons',
          icon: '🎨',
          description: '自定义图标库',
          color: 'primary'
        },
        msgx: {
          displayName: 'Msgx',
          icon: '💬',
          description: '消息提示组件',
          color: 'primary'
        }
      },
      mockDataGenerators: {
        basic: () => ({
          currentComponent: 'basic',
          componentProps: {}
        }),
        ykNavBar: () => ({
          currentComponent: 'YkNavBar',
          componentProps: {
            title: '自定义导航栏',
            showBack: true,
            showHome: true
          }
        }),
        ykSwitchTabBar: () => ({
          currentComponent: 'YkSwitchTabBar',
          componentProps: {
            tabs: ['标签1', '标签2', '标签3'],
            activeIndex: 0
          }
        }),
        ykImagePicker: () => ({
          currentComponent: 'YkImagePicker',
          componentProps: {
            type: 'idCardFront',
            label: '身份证正面',
            value: null
          }
        }),
        ykNoticeBar: () => ({
          currentComponent: 'YkNoticeBar',
          componentProps: {
            type: 'warning',
            title: '重要提示',
            content: '这是一个警告类型的通知栏示例'
          }
        }),
        ykCellLabel: () => ({
          currentComponent: 'YkCellLabel',
          componentProps: {
            label: '标签名称',
            value: '标签值',
            required: true
          }
        }),
        bottomPopup: () => ({
          currentComponent: 'BottomPopup',
          componentProps: {
            visible: true,
            title: '底部弹窗标题'
          }
        }),
        fullScreenLoading: () => ({
          currentComponent: 'FullScreenLoading',
          componentProps: {
            visible: true,
            text: '正在加载中...'
          }
        }),
        pageAnimation: () => ({
          currentComponent: 'PageAnimation',
          componentProps: {}
        }),
        permissionPopup: () => ({
          currentComponent: 'PermissionPopup',
          componentProps: {
            visible: true,
            type: 'camera',
            title: '相机权限申请'
          }
        }),
        protocolMask: () => ({
          currentComponent: 'ProtocolMask',
          componentProps: {
            visible: true,
            title: '用户协议'
          }
        }),
        ykAreaPicker: () => ({
          currentComponent: 'YkAreaPicker',
          componentProps: {
            placeholder: '请选择地区',
            value: ''
          }
        }),
        ykDatePicker: () => ({
          currentComponent: 'YkDatePicker',
          componentProps: {
            placeholder: '请选择日期',
            value: ''
          }
        }),
        ykIndustryPicker: () => ({
          currentComponent: 'YkIndustryPicker',
          componentProps: {
            placeholder: '请选择行业',
            value: ''
          }
        }),
        ykIcons: () => ({
          currentComponent: 'YkIcons',
          componentProps: {}
        }),
        msgx: () => ({
          currentComponent: 'Msgx',
          componentProps: {
            direction: 'topCenter',
            content: '这是一个测试消息',
            visible: true,
            backgroundColor: '#007AFF',
            textColor: '#fff'
          }
        })
      }
    })
  );

  // 页面加载时处理调试模式
  useEffect(() => {
    if (testDebugger.isDebugMode()) {
      const scenario = testDebugger.getDebugScenario();
      testDebugger.debugLog(`[basic] 调试模式已启用，当前场景: ${scenario}`);

      // 如果有特定场景，加载对应的模拟数据
      if (scenario !== 'basic' && scenario !== '') {
        loadMockData(scenario);
      }
    }
  }, []);

  // 加载模拟数据
  const loadMockData = async (scenario: string) => {
    testDebugger.debugLog(`[basic] 开始加载模拟数据，场景: ${scenario}`);

    const mockData = testDebugger.generateMockData<ComponentDemoData>(scenario);
    if (mockData) {
      setDemoData(mockData);
      testDebugger.debugLog(`[mock] 模拟数据加载完成`, mockData);
      
      // 根据组件类型设置特殊状态
      if (mockData.currentComponent === 'BottomPopup') {
        setComponentStates(prev => ({ ...prev, showBottomPopup: true }));
      } else if (mockData.currentComponent === 'FullScreenLoading') {
        setComponentStates(prev => ({ ...prev, showFullScreenLoading: true }));
      } else if (mockData.currentComponent === 'PermissionPopup') {
        setComponentStates(prev => ({ ...prev, showPermissionPopup: true }));
      } else if (mockData.currentComponent === 'ProtocolMask') {
        setComponentStates(prev => ({ ...prev, showProtocolMask: true }));
      }
    } else {
      testDebugger.debugLog(`[mock] 未找到场景 ${scenario} 的模拟数据生成器`);
    }
  };

  // 获取状态信息用于调试面板显示
  const getStatusInfo = () => {
    const iconsInfo = getIconsInfo();
    return {
      '当前组件': demoData.currentComponent,
      '组件属性': JSON.stringify(demoData.componentProps, null, 2),
      '调试场景': testDebugger.getDebugScenario(),
      '弹窗状态': `底部弹窗: ${componentStates.showBottomPopup ? '显示' : '隐藏'}`,
      '加载状态': componentStates.showFullScreenLoading ? '显示' : '隐藏',
      '图标总数': iconsInfo.total,
      '刷新次数': iconRefreshKey
    };
  };

  // 渲染当前选中的组件
  const renderCurrentComponent = () => {
    const { currentComponent, componentProps } = demoData;
    
    switch (currentComponent) {
      case 'YkNavBar':
        return <YkNavBar {...componentProps} />;
      case 'YkSwitchTabBar':
        return <YkSwitchTabBar {...componentProps} />;
      case 'YkImagePicker':
        return <YkImagePicker {...componentProps} />;
      case 'YkNoticeBar':
        return <YkNoticeBar {...componentProps} />;
      case 'YkCellLabel':
        return <YkCellLabel {...componentProps} />;
      case 'BottomPopup':
        return <BottomPopup {...componentProps} />;
      case 'FullScreenLoading':
        return <FullScreenLoading {...componentProps} />;
      case 'PageAnimation':
        return <PageAnimation {...componentProps} />;
      case 'PermissionPopup':
        return <PermissionPopup {...componentProps} />;
      case 'ProtocolMask':
        return <ProtocolMask {...componentProps} />;
      case 'YkAreaPicker':
        return <YkAreaPicker {...componentProps} />;
      case 'YkDatePicker':
        return <YkDatePicker {...componentProps} />;
      case 'YkIndustryPicker':
        return <YkIndustryPicker {...componentProps} />;
      case 'YkIcons':
        const iconsList = getAllIcons();
        const iconsInfo = getIconsInfo();

        return (
          <View className="icons-showcase" key={iconRefreshKey}>
            <View className="icons-header">
              <Text className="showcase-title">🎨 自定义图标库</Text>
              <View
                className="refresh-button"
                onClick={() => {
                  setIconRefreshKey(prev => prev + 1);
                  const newInfo = getIconsInfo();
                  testDebugger.debugLog('[icons] 刷新图标库', {
                    oldCount: iconsInfo.total,
                    newCount: newInfo.total,
                    refreshTime: newInfo.lastUpdated
                  });
                }}
              >
                <Text>🔄 刷新</Text>
              </View>
            </View>
            <Text className="icons-count">共 {iconsList.length} 个图标</Text>
            <Text className="icons-info">最后更新: {new Date(iconsInfo.lastUpdated).toLocaleTimeString()}</Text>
            <View className="icons-grid">
              {iconsList.map(({ name, component: IconComponent, displayName }) => (
                <View key={name} className="icon-item">
                  <IconComponent size={24} />
                  <Text className="icon-name">{name}</Text>
                  <Text className="icon-display-name">{displayName}</Text>
                </View>
              ))}
            </View>
          </View>
        );
      case 'Msgx':
        return (
          <View className="msgx-showcase">
            <View className="msgx-header">
              <Text className="showcase-title">💬 Msgx 消息提示组件</Text>
              <Text className="showcase-description">
                支持 12 个方向的消息提示组件，参照 Popover 实现
              </Text>
            </View>

            <View className="msgx-demo-grid">
              {/* 上方位置 */}
              <View className="direction-group">
                <Text className="group-title">上方位置</Text>
                <View className="demo-row">
                  <Msgx direction="topLeft" content="左上角提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">topLeft</View>
                  </Msgx>
                  <Msgx direction="topCenter" content="正上方提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">topCenter</View>
                  </Msgx>
                  <Msgx direction="topRight" content="右上角提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">topRight</View>
                  </Msgx>
                </View>
              </View>

              {/* 下方位置 */}
              <View className="direction-group">
                <Text className="group-title">下方位置</Text>
                <View className="demo-row">
                  <Msgx direction="bottomLeft" content="左下角提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">bottomLeft</View>
                  </Msgx>
                  <Msgx direction="bottomCenter" content="正下方提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">bottomCenter</View>
                  </Msgx>
                  <Msgx direction="bottomRight" content="右下角提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">bottomRight</View>
                  </Msgx>
                </View>
              </View>

              {/* 左侧位置 */}
              <View className="direction-group">
                <Text className="group-title">左侧位置</Text>
                <View className="demo-row">
                  <Msgx direction="leftTop" content="左上提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">leftTop</View>
                  </Msgx>
                  <Msgx direction="leftCenter" content="左中提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">leftCenter</View>
                  </Msgx>
                  <Msgx direction="leftBottom" content="左下提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">leftBottom</View>
                  </Msgx>
                </View>
              </View>

              {/* 右侧位置 */}
              <View className="direction-group">
                <Text className="group-title">右侧位置</Text>
                <View className="demo-row">
                  <Msgx direction="rightTop" content="右上提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">rightTop</View>
                  </Msgx>
                  <Msgx direction="rightCenter" content="右中提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">rightCenter</View>
                  </Msgx>
                  <Msgx direction="rightBottom" content="右下提示" visible={componentStates.msgxVisible}>
                    <View className="demo-trigger">rightBottom</View>
                  </Msgx>
                </View>
              </View>

              {/* 控制按钮 */}
              <View className="control-buttons">
                <View
                  className="control-button"
                  onClick={() => {
                    setComponentStates(prev => ({
                      ...prev,
                      msgxVisible: !prev.msgxVisible
                    }));
                    testDebugger.debugLog('[msgx] 切换显示状态', {
                      visible: !componentStates.msgxVisible
                    });
                  }}
                >
                  <Text>{componentStates.msgxVisible ? '隐藏' : '显示'} 消息提示</Text>
                </View>
              </View>

              {/* 自定义样式示例 */}
              <View className="custom-style-demo">
                <Text className="group-title">自定义样式示例</Text>
                <View className="demo-row">
                  <Msgx
                    direction="topCenter"
                    content="成功提示"
                    visible={componentStates.msgxVisible}
                    backgroundColor="#00B42A"
                    textColor="#fff"
                    borderRadius={8}
                    padding="12px 16px"
                    arrowSize={10}
                  >
                    <View className="demo-trigger success">成功</View>
                  </Msgx>
                  <Msgx
                    direction="bottomCenter"
                    content="警告提示"
                    visible={componentStates.msgxVisible}
                    backgroundColor="#FF7D00"
                    textColor="#fff"
                    borderRadius={8}
                    padding="12px 16px"
                    arrowSize={10}
                  >
                    <View className="demo-trigger warning">警告</View>
                  </Msgx>
                  <Msgx
                    direction="rightCenter"
                    content="错误提示"
                    visible={componentStates.msgxVisible}
                    backgroundColor="#F53F3F"
                    textColor="#fff"
                    borderRadius={8}
                    padding="12px 16px"
                    arrowSize={10}
                  >
                    <View className="demo-trigger error">错误</View>
                  </Msgx>
                </View>
              </View>
            </View>
          </View>
        );
      default:
        return (
          <View className="component-showcase">
            <Text className="showcase-title">🧩 自定义组件展示</Text>
            <Text className="showcase-description">
              选择左侧调试面板中的组件场景来查看对应的组件展示
            </Text>
          </View>
        );
    }
  };

  return (
    <View className="debug-test">
      <View className="test-content">
        <PageAnimation>
          {/* 页面标题 */}
          <View className="test-card">
            <Text className="test-title">🧩 自定义组件展示</Text>
            <Text className="test-description">
              展示src/components目录下的所有自定义组件
            </Text>
          </View>

          {/* 组件展示区域 */}
          <View className="component-display">
            {renderCurrentComponent()}
          </View>
        </PageAnimation>
      </View>

      {/* 调试面板 - 只在调试模式下显示 */}
      {testDebugger.isDebugMode() && (
        <DebugPanel
          scenario={testDebugger.getDebugScenario()}
          config={testDebugger.config}
          onLoadMockData={loadMockData}
          statusInfo={getStatusInfo()}
          position="bottom-right"
        />
      )}
    </View>
  );
};

export default ComponentTest;
