#!/usr/bin/env node

// test-ai-sse.js - AI 聊天 SSE 接口测试脚本
// 使用方法: node test-ai-sse.js

import PostEventSource, { createAiChatSSEClient } from './fetch-post-sse-parser.js';

// 检查是否在 Node.js 环境中
if (typeof window !== 'undefined') {
  console.error('❌ 此脚本需要在 Node.js 环境中运行');
  process.exit(1);
}

// 测试配置
const testConfig = {
  conversationId: '1781604279872581810',
  content: '帮我写个c++ hellworld算法',
  useContext: true,
  // 可以自定义这些参数
  baseUrl: 'http://123.6.102.143:38080',
  tenantId: '1',
  authorization: 'Bearer ee12a8beaca5445ab4740133cd1b6016'
};

console.log('🚀 开始测试 AI 聊天 SSE 接口...');
console.log('📋 测试配置:', {
  conversationId: testConfig.conversationId,
  content: testConfig.content,
  useContext: testConfig.useContext,
  baseUrl: testConfig.baseUrl
});

// 创建 AI 聊天 SSE 客户端
const aiChatClient = createAiChatSSEClient(testConfig);

let messageCount = 0;
let isCompleted = false;
let lastContent = ''; // 上次接收到的完整内容

// 监听消息事件
aiChatClient.addEventListener('message', e => {
  messageCount++;
  try {
    const data = JSON.parse(e.data);
    
    if (data.data) {
      if (data.data.send && messageCount === 1) {
        // 只在第一条消息时显示发送的内容
        console.log(`\n📤 发送消息: ${data.data.send.content}`);
        console.log('🤖 AI 正在回复...\n');
      }
      
      if (data.data.receive && data.data.receive.content) {
        const currentContent = data.data.receive.content;
        
        // 计算新增的内容（增量部分）
        let newPart = '';
        if (currentContent.length > lastContent.length) {
          // 如果当前内容比上次内容长，说明有新内容
          newPart = currentContent.substring(lastContent.length);
        } else if (currentContent !== lastContent) {
          // 如果内容不同但长度相同，可能是内容被替换
          newPart = currentContent;
        }
        
        // 只打印新增的部分
        if (newPart) {
          process.stdout.write(newPart);
          
          // 如果内容包含句号、问号、感叹号等，添加换行
          if (newPart.match(/[。！？.!?]\s*$/)) {
            process.stdout.write('\n');
          }
        }
        
        // 更新上次内容
        lastContent = currentContent;
      }
    }
  } catch (error) {
    console.log('\n📨 原始消息数据:', e.data);
  }
});

// 监听错误事件
aiChatClient.addEventListener('error', err => {
  console.error('\n❌ SSE 连接错误:', err);
  isCompleted = true;
});

// 监听连接关闭
aiChatClient.addEventListener('close', () => {
  console.log('\n\n🔌 SSE 连接已关闭');
  console.log(`📊 总共收到 ${messageCount} 条消息`);
  console.log(`📝 AI 回复总长度: ${lastContent.length} 字符`);
  isCompleted = true;
});

// 设置超时自动关闭
const timeout = setTimeout(() => {
  if (!isCompleted) {
    console.log('\n⏰ 30秒超时，自动关闭连接...');
    aiChatClient.close();
  }
}, 30000);

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在关闭连接...');
  clearTimeout(timeout);
  aiChatClient.close();
  process.exit(0);
});

console.log('✅ 测试客户端已启动，等待 AI 回复...');
console.log('💡 按 Ctrl+C 可以手动停止测试\n');
