// 原生桥接配置和使用示例
// Native Bridge Configuration and Usage Examples

import NativeBridge, { PERMISSION_TYPES, PAY_STATUS, PermissionInfo, WxUserInfo } from './nativeBridge';

// ==================== 基础环境检测示例 ====================

/**
 * 检测当前运行环境
 */
export function detectEnvironment() {
  const info = {
    isInWebView: NativeBridge.isInWebView(),
    isAndroidWebView: NativeBridge.isAndroidWebView(),
    isIOSWebView: NativeBridge.isIOSWebView(),
    userAgent: navigator.userAgent
  };
  
  console.log('🔍 环境检测结果:', info);
  return info;
}

// ==================== 应用控制类示例 ====================

/**
 * 示例：优雅退出应用
 */
export function exitAppGracefully() {
  if (NativeBridge.isInWebView()) {
    console.log('🚪 准备退出应用...');
    // 可以在这里添加一些清理逻辑
    // 比如保存用户数据、清理缓存等
    
    NativeBridge.exitApp();
  } else {
    console.warn('⚠️ 不在WebView环境中，无法退出应用');
    // 在浏览器环境中的替代方案
    window.close();
  }
}

/**
 * 示例：检测是否为第三方应用调用
 */
export function checkIfFromOtherApp() {
  if (NativeBridge.isAndroidWebView()) {
    NativeBridge.isOtherApp();
    console.log('🔍 已请求检测是否来自第三方应用');
  } else {
    console.log('ℹ️ 仅在Android WebView中支持第三方应用检测');
  }
}

// ==================== 微信功能示例 ====================

/**
 * 示例：完整的微信登录流程
 */
export async function performWechatLogin(): Promise<WxUserInfo | null> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('微信登录仅在原生应用中可用');
    }

    console.log('🔐 开始微信登录...');
    const userInfo = await NativeBridge.wxLogin();
    
    console.log('✅ 微信登录成功:', {
      nickname: userInfo.nickname,
      unionid: userInfo.unionid,
      hasAvatar: !!userInfo.head_img
    });
    
    // 这里可以添加登录后的业务逻辑
    // 比如保存用户信息到本地存储
    localStorage.setItem('wx_user_info', JSON.stringify(userInfo));
    
    return userInfo;
  } catch (error) {
    console.error('❌ 微信登录失败:', error);
    return null;
  }
}

/**
 * 示例：微信支付流程
 */
export async function performWechatPay(paymentData: string): Promise<boolean> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('微信支付仅在原生应用中可用');
    }

    console.log('💰 开始微信支付...');
    const payResult = await NativeBridge.wxPay(paymentData);
    
    if (payResult === PAY_STATUS.SUCCESS) {
      console.log('✅ 微信支付成功');
      return true;
    } else {
      console.log('❌ 微信支付失败，状态:', payResult);
      return false;
    }
  } catch (error) {
    console.error('❌ 微信支付异常:', error);
    return false;
  }
}

/**
 * 示例：分享图片到微信
 */
export function shareImageToWechat(imageData: string, shareType: 'friend' | 'circle' | 'favorite' = 'friend') {
  if (!NativeBridge.isInWebView()) {
    console.warn('⚠️ 分享功能仅在原生应用中可用');
    return;
  }

  console.log(`📤 分享图片到微信${shareType === 'friend' ? '好友' : shareType === 'circle' ? '朋友圈' : '收藏'}...`);

  try {
    if (imageData.startsWith('data:image') || imageData.startsWith('iVBOR')) {
      // Base64 图片
      switch (shareType) {
        case 'friend':
          NativeBridge.shareBase64ImageToWechat(imageData);
          break;
        case 'circle':
          NativeBridge.shareBase64ImageToWechatCircle(imageData);
          break;
        case 'favorite':
          // 注意：收藏功能使用的是URL分享，这里需要先上传图片获取URL
          console.warn('⚠️ 收藏功能需要图片URL，不支持Base64');
          break;
      }
    } else {
      // URL 图片
      switch (shareType) {
        case 'friend':
          NativeBridge.shareImageToWechat(imageData);
          break;
        case 'favorite':
          NativeBridge.shareImageToWechatFavorite(imageData);
          break;
        default:
          console.warn('⚠️ 朋友圈分享仅支持Base64图片');
      }
    }
    
    console.log('✅ 分享请求已发送');
  } catch (error) {
    console.error('❌ 分享失败:', error);
  }
}

// ==================== 文件下载示例 ====================

/**
 * 示例：下载并保存图片到相册
 */
export async function downloadAndSaveImage(imageUrl: string): Promise<boolean> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('图片下载仅在原生应用中可用');
    }

    console.log('📥 开始下载图片:', imageUrl);
    await NativeBridge.downloadImage(imageUrl);
    console.log('✅ 图片下载并保存成功');
    return true;
  } catch (error) {
    console.error('❌ 图片下载失败:', error);
    return false;
  }
}

/**
 * 示例：保存Base64图片到相册
 */
export async function saveBase64ImageToAlbum(base64Data: string): Promise<boolean> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('图片保存仅在原生应用中可用');
    }

    console.log('💾 开始保存Base64图片到相册...');
    
    // 1. 先检查存储权限
    console.log('🔍 检查存储权限...');
    const permissions = await NativeBridge.checkPermission();
    
    if (!permissions.openStorage) {
      console.log('❌ 存储权限未授权，请求权限...');
      const granted = await NativeBridge.requestPermission('STORAGE');
      
      if (!granted) {
        console.log('❌ 用户拒绝存储权限');
        throw new Error('存储权限未授权，无法保存图片');
      }
      console.log('✅ 存储权限已授权');
    } else {
      console.log('✅ 存储权限已存在');
    }

    // 2. 验证和处理图片数据
    let processedData = base64Data;
    if (base64Data.startsWith('data:image/')) {
      processedData = base64Data.split(',')[1];
    }

    if (!processedData || processedData.length < 100) {
      throw new Error('图片数据无效');
    }

    // 检查图片大小
    const dataSizeKB = (processedData.length * 3) / 4 / 1024;
    console.log(`📊 图片大小: ${dataSizeKB.toFixed(2)} KB`);
    
    if (dataSizeKB > 5120) { // 超过5MB
      throw new Error('图片过大，无法保存');
    }

    // 3. 保存图片
    console.log('💾 保存图片到相册...');
    const success = await NativeBridge.saveImageToAlbumNative(processedData);
    
    if (success) {
      console.log('✅ 图片保存到相册成功');
      return true;
    } else {
      console.log('❌ 图片保存到相册失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 图片保存异常:', error);
    
    // 提供更详细的错误信息
    if (error.message.includes('权限')) {
      console.log('💡 建议：引导用户到设置页面开启存储权限');
    } else if (error.message.includes('过大')) {
      console.log('💡 建议：压缩图片后重试');
    } else if (error.message.includes('超时')) {
      console.log('💡 建议：检查网络连接或稍后重试');
    }
    
    return false;
  }
}

// ==================== 权限管理示例 ====================

/**
 * 示例：检查并请求相机权限
 */
export async function requestCameraPermission(): Promise<boolean> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('权限管理仅在原生应用中可用');
    }

    // 先检查当前权限状态
    console.log('🔍 检查相机权限状态...');
    const permissions = await NativeBridge.checkPermission();
    
    if (permissions.openCamera) {
      console.log('✅ 相机权限已授权');
      return true;
    }

    // 请求相机权限
    console.log('📷 请求相机权限...');
    const granted = await NativeBridge.requestPermission('CAMERA');
    
    if (granted) {
      console.log('✅ 相机权限已授权');
      return true;
    } else {
      console.log('❌ 相机权限被拒绝');
      // 可以引导用户到设置页面
      console.log('💡 可以通过 NativeBridge.openSetting() 引导用户到设置页面');
      return false;
    }
  } catch (error) {
    console.error('❌ 权限请求失败:', error);
    return false;
  }
}

/**
 * 示例：批量检查权限状态
 */
export async function checkAllPermissions(): Promise<PermissionInfo | null> {
  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('权限检查仅在原生应用中可用');
    }

    console.log('🔍 检查所有权限状态...');
    const permissions = await NativeBridge.checkPermission();
    
    console.log('📋 权限状态:', {
      相机: permissions.openCamera ? '✅' : '❌',
      相册图片: permissions.openGalleryPhoto ? '✅' : '❌',
      相册视频: permissions.openGalleryVideo ? '✅' : '❌',
      相册音频: permissions.openGalleryAudio ? '✅' : '❌',
      存储: permissions.openStorage ? '✅' : '❌',
      通讯录: permissions.openContacts ? '✅' : '❌',
      位置: permissions.openLocation ? '✅' : '❌',
      麦克风: permissions.openAudio ? '✅' : '❌',
      通知: permissions.openNotice ? '✅' : '❌'
    });
    
    return permissions;
  } catch (error) {
    console.error('❌ 权限检查失败:', error);
    return null;
  }
}

// ==================== UI控制示例 ====================

/**
 * 示例：根据页面主题设置状态栏
 */
export function setStatusBarTheme(isDarkTheme: boolean) {
  if (!NativeBridge.isInWebView()) {
    console.log('ℹ️ 状态栏控制仅在原生应用中可用');
    return;
  }

  if (isDarkTheme) {
    // 深色主题使用浅色状态栏（白字）
    NativeBridge.setStatusBarLightMode();
    console.log('🌙 已设置状态栏为浅色模式（深色主题）');
  } else {
    // 浅色主题使用深色状态栏（黑字）
    NativeBridge.setStatusBarDarkMode();
    console.log('☀️ 已设置状态栏为深色模式（浅色主题）');
  }
}

/**
 * 示例：设置照片选择器配置
 */
export function configurePhotoSelector(maxCount: number = 9) {
  if (!NativeBridge.isInWebView()) {
    console.log('ℹ️ 照片选择器配置仅在原生应用中可用');
    return;
  }

  NativeBridge.setPhotoSelectNum(maxCount);
  console.log(`📷 已设置照片选择最大数量: ${maxCount}`);
}

// ==================== 小程序功能示例 ====================

/**
 * 示例：打开小程序用户页面
 */
export function openMiniProgramUserPage(userId: string) {
  if (!NativeBridge.isInWebView()) {
    console.log('ℹ️ 小程序功能仅在原生应用中可用');
    return;
  }

  console.log('🚀 打开小程序用户页面:', userId);
  NativeBridge.openMiniProgramUser(userId);
}

/**
 * 示例：打开小程序动态页面
 */
export function openMiniProgramTrendPage(dynamicId: string) {
  if (!NativeBridge.isInWebView()) {
    console.log('ℹ️ 小程序功能仅在原生应用中可用');
    return;
  }

  console.log('🚀 打开小程序动态页面:', dynamicId);
  NativeBridge.openMiniProgramTrend(dynamicId);
}

// ==================== 综合示例 ====================

/**
 * 示例：完整的图片处理流程
 * 包括权限检查、图片处理、保存和分享
 */
export async function completeImageWorkflow(imageData: string, options: {
  saveToAlbum?: boolean;
  shareToWechat?: boolean;
  shareType?: 'friend' | 'circle' | 'favorite';
} = {}) {
  const { saveToAlbum = false, shareToWechat = false, shareType = 'friend' } = options;

  try {
    if (!NativeBridge.isInWebView()) {
      throw new Error('图片处理流程仅在原生应用中可用');
    }

    console.log('🖼️ 开始图片处理流程...');

    // 1. 检查存储权限（如果需要保存）
    if (saveToAlbum) {
      console.log('1️⃣ 检查存储权限...');
      const permissions = await NativeBridge.checkPermission();
      if (!permissions.openStorage) {
        const granted = await NativeBridge.requestPermission('STORAGE');
        if (!granted) {
          throw new Error('存储权限未授权，无法保存图片');
        }
      }

      // 2. 保存图片到相册
      console.log('2️⃣ 保存图片到相册...');
      const saveSuccess = await saveBase64ImageToAlbum(imageData);
      if (!saveSuccess) {
        throw new Error('图片保存失败');
      }
    }

    // 3. 分享到微信（如果需要）
    if (shareToWechat) {
      console.log('3️⃣ 分享图片到微信...');
      shareImageToWechat(imageData, shareType);
    }

    console.log('✅ 图片处理流程完成');
    return true;
  } catch (error) {
    console.error('❌ 图片处理流程失败:', error);
    return false;
  }
}

/**
 * 应用初始化配置示例
 */
export function initializeApp() {
  console.log('🚀 初始化应用配置...');

  // 检测环境
  const env = detectEnvironment();
  
  if (env.isInWebView) {
    console.log('📱 在原生应用环境中运行');
    
    // 设置默认的状态栏主题（根据你的应用主题调整）
    setStatusBarTheme(false); // false = 浅色主题
    
    // 设置默认的照片选择器配置
    configurePhotoSelector(9);
    
    // 获取并显示调试信息
    const debugInfo = NativeBridge.getDebugInfo();
    console.log('🔍 调试信息:', debugInfo);
    
    console.log('✅ 原生功能初始化完成');
  } else {
    console.log('🌐 在浏览器环境中运行');
  }
}

// ==================== 常用工具函数 ====================

/**
 * 检查图片格式是否为Base64
 */
export function isBase64Image(data: string): boolean {
  return data.startsWith('data:image') || /^[A-Za-z0-9+/]+=*$/.test(data);
}

/**
 * 获取当前设备信息
 */
export function getDeviceInfo() {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    isWebView: NativeBridge.isInWebView(),
    isAndroid: NativeBridge.isAndroidWebView(),
    isIOS: NativeBridge.isIOSWebView(),
    debugInfo: NativeBridge.getDebugInfo()
  };
}

// ==================== 图片保存专用工具 ====================

/**
 * 智能图片保存工具
 * 自动处理权限检查、数据验证、错误处理和用户提示
 */
export interface ImageSaveOptions {
  showLoading?: boolean;          // 是否显示加载提示
  showSuccessToast?: boolean;     // 是否显示成功提示
  showErrorToast?: boolean;       // 是否显示错误提示
  autoRequestPermission?: boolean; // 是否自动请求权限
  maxSizeKB?: number;            // 最大文件大小限制（KB）
  timeoutMS?: number;            // 超时时间（毫秒）
}

export async function saveImageSmart(
  imageData: string, 
  options: ImageSaveOptions = {}
): Promise<{ success: boolean; error?: string; action?: string }> {
  const {
    showLoading = true,
    showSuccessToast = true,
    showErrorToast = true,
    autoRequestPermission = true,
    maxSizeKB = 5120, // 5MB
    timeoutMS = 30000 // 30秒
  } = options;

  try {
    // 1. 环境检查
    if (!NativeBridge.isInWebView()) {
      const error = '图片保存功能仅在原生应用中可用';
      if (showErrorToast) {
        // 这里应该调用实际的 Toast 显示方法
        console.error(error);
      }
      return { success: false, error, action: 'env_check_failed' };
    }

    if (showLoading) {
      // 这里应该调用实际的 Loading 显示方法
      console.log('正在准备保存图片...');
    }

    // 2. 权限检查
    try {
      const permissions = await NativeBridge.checkPermission();
      if (!permissions.openStorage) {
        if (autoRequestPermission) {
          const granted = await NativeBridge.requestPermission('STORAGE');
          if (!granted) {
            if (showLoading) console.log('隐藏Loading');
            return { 
              success: false, 
              error: '存储权限未授权', 
              action: 'permission_denied' 
            };
          }
        } else {
          if (showLoading) console.log('隐藏Loading');
          return { 
            success: false, 
            error: '需要存储权限', 
            action: 'permission_required' 
          };
        }
      }
    } catch (permissionError) {
      if (showLoading) console.log('隐藏Loading');
      return { 
        success: false, 
        error: '权限检查失败: ' + permissionError.message, 
        action: 'permission_check_failed' 
      };
    }

    // 3. 数据验证和处理
    let processedData = imageData;
    if (imageData.startsWith('data:image/')) {
      processedData = imageData.split(',')[1];
    }

    if (!processedData || processedData.length < 100) {
      if (showLoading) console.log('隐藏Loading');
      return { 
        success: false, 
        error: '图片数据无效', 
        action: 'invalid_data' 
      };
    }

    // 4. 大小检查
    const dataSizeKB = (processedData.length * 3) / 4 / 1024;
    if (dataSizeKB > maxSizeKB) {
      if (showLoading) console.log('隐藏Loading');
      return { 
        success: false, 
        error: `图片过大 (${dataSizeKB.toFixed(2)}KB > ${maxSizeKB}KB)`, 
        action: 'size_exceeded' 
      };
    }

    // 5. 保存图片
    try {
      const success = await Promise.race([
        NativeBridge.saveImageToAlbumNative(processedData),
        new Promise<boolean>((_, reject) => 
          setTimeout(() => reject(new Error('操作超时')), timeoutMS)
        )
      ]);

      if (showLoading) console.log('隐藏Loading');

      if (success) {
        if (showSuccessToast) {
          console.log('✅ 图片保存成功');
        }
        return { success: true };
      } else {
        return { 
          success: false, 
          error: '保存操作失败', 
          action: 'save_failed' 
        };
      }
    } catch (saveError) {
      if (showLoading) console.log('隐藏Loading');
      return { 
        success: false, 
        error: '保存异常: ' + saveError.message, 
        action: 'save_exception' 
      };
    }

  } catch (error) {
    if (showLoading) console.log('隐藏Loading');
    return { 
      success: false, 
      error: '未知错误: ' + error.message, 
      action: 'unknown_error' 
    };
  }
}

/**
 * 获取保存失败的建议操作
 */
export function getSaveFailureSuggestion(action: string): string {
  switch (action) {
    case 'env_check_failed':
      return '请在原生应用中使用此功能';
    case 'permission_denied':
      return '请前往设置页面开启存储权限';
    case 'permission_required':
      return '需要存储权限才能保存图片';
    case 'permission_check_failed':
      return '权限检查失败，请重试';
    case 'invalid_data':
      return '图片数据无效，请重新生成';
    case 'size_exceeded':
      return '图片过大，建议压缩后重试';
    case 'save_failed':
      return '保存失败，请检查存储空间';
    case 'save_exception':
      return '保存异常，请稍后重试';
    default:
      return '保存失败，请重试或联系技术支持';
  }
}

// ==================== 使用说明 ====================

/*
使用方法：

1. 在应用入口文件（如 App.tsx 或 main.ts）中导入并初始化：
   ```typescript
   import { initializeApp } from '@/utils/bridgeConfig.example';
   
   // 在应用启动时初始化
   initializeApp();
   ```

2. 在组件中使用具体功能：
   ```typescript
   import { 
     performWechatLogin, 
     shareImageToWechat, 
     requestCameraPermission,
     completeImageWorkflow
   } from '@/utils/bridgeConfig.example';
   
   // 微信登录
   const handleWechatLogin = async () => {
     const userInfo = await performWechatLogin();
     if (userInfo) {
       // 登录成功处理
     }
   };
   
   // 分享图片
   const handleShare = () => {
     shareImageToWechat(base64Image, 'friend');
   };
   
   // 完整的图片处理流程
   const handleImageProcess = async () => {
     await completeImageWorkflow(imageData, {
       saveToAlbum: true,
       shareToWechat: true,
       shareType: 'friend'
     });
   };
   ```

3. Android端对应的配置（已在WebViewActivity.java中实现）：
   - 确保所有接口都已正确注入到WebView中
   - 确保回调函数能正确调用JavaScript端的方法

4. 调试和测试：
   - 使用 WebViewTester 工具进行功能测试
   - 在开发环境中启用调试日志
   - 使用 getDeviceInfo() 获取详细的设备和环境信息
*/ 