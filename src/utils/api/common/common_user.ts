import request from '@/utils/api/request';
import {requestUploadBlobFiles, requestUploadFile } from '@/utils/api/requestUploadFile';
//ppxc的接口 前缀加上ppxc   'ppxc/method/uploadHeadImg' 

export * from './common_wechat';

/**
 * @module albumLogin 用户登录 /method/login POST
 * @param head_img	否	string	微信头像 type=1 有值
 * @param nickname	否	string	微信昵称 type=1 有值
 * @param channel_name	否	string	渠道 type=1 有值
 * @param unionid	否	string	type=1 有值
 * @param mobile	否	string	手机号 type=2和type=3 有值
 * @param password	否	string	密码 type=2 有值
 * @param code	否	string	验证码 type=3 有值
 * @param type	是	string	类型:type 1:微信账号登陆 2:密码登陆 3:验证码登陆 4:修改登录密码
 * */
interface albumLoginData {
	head_img?: string,
	nickname?: string,
	channel_name?: string,
	unionid?: string,
	mobile?: string,
	password?: string,
	code?: string,
	type: number
}
export const albumLogin = (data:albumLoginData) => {
	return request("/app-api/member/auth/login", "POST", data)
}

export const login = (data:albumLoginData) => {
	return request("/app-api/member/auth/login", "POST", data)
}


/**
 * @module socialLogin 社交登录 /app-api/member/auth/social-login POST
 * @param type	是	string	类型:type 1:微信账号登陆 2:密码登陆 3:验证码登陆 4:修改登录密码
 * @param code	否	string	验证码 type=3 有值
 * */
interface socialLoginData {
	type: number,
	code: string,
	state: string
}
export const socialLogin = (data:socialLoginData) => {
	return request("/app-api/member/auth/social-login", "POST", data)
}

/**
 * @module smsLogin 短信登录 /app-api/member/auth/sms-login POST
 * @param mobile	是	string	手机号
 * @param code	是	string	验证码
 * */
interface smsLoginData {
	mobile: string,
	code: string
}
export const smsLogin = (data:smsLoginData) => {
	return request("/app-api/member/auth/sms-login", "POST", data)
}



export const getUserInfo = () => {
	return request("/app-api/member/user/get", "GET")
}
/**
 * @module getAlbumList 获取相册首页动态 /method/getAlbumList POST
 * @param page	否	string	第几页
 * @param limit	否	string	每页几条
 * */

interface getAlbumListData {
	pageNo?: number,
	pageSize?: number,
	userId?: string
}
export const getAlbumList = (data:getAlbumListData) => {
	return request("/app-api/album/dynamics/get-home-page", "GET", data)
}

export const getNewAlbumList = (data:getAlbumListData) => {
	return request("/app-api/album/dynamics/get-new-page", "GET", data)
}


/**
 * @module getMyAlbumList 我的相册列表
 */
interface getMyAlbumListData {
	pageNo?: number,
	pageSize?: number,
	userId?: string
}
export const getMyAlbumList = (data:getMyAlbumListData) => {
	return request("/app-api/album/dynamics/get-user-home-page", "GET", data)
}


/**
 * @module getUserHomeTopData 我的相册头部数据
 */
export const getUserHomeTopData = (data) => {
	return request("/app-api/album/dynamics/get-user-home-top?userId=" + data.userId, "GET", data)
}

/**
 * @module deleteDynamic 删除动态
 */
export const deleteDynamic = (data) => {
	return request('/app-api/album/dynamics/delete?id='+data.id, 'DELETE')
}

/**
 * @module upAndDownShelvesDynamic 上下架动态 /app-api/album/dynamics/update-status DELETE
 */
export const upAndDownShelvesDynamic = (data) => {
	return request('/app-api/album/dynamics/update-status?ids=' + data.ids + '&status=' + data.status, 'PUT' )
}


/**
 * 动态搜索
 */
interface getSearchAlbumListData {
	page?: number,
	limit?: number,
	content?: string,
	type?: number
}
export const getSearchAlbumList = (data:getSearchAlbumListData) => {
	return request("/method/getSearchAlbumList", "POST", data)
}


/**
 * @module dynamicBatchDel 删除动态 /method/dynamicBatchDel POST
 * @param id		是	string	动态id
 * */
interface dynamicBatchDelData {
	id: string
}
export const dynamicBatchDel = (data:dynamicBatchDelData) => {
	return request("/app-api/album/dynamics/delete", "DELETE", data)
}


// favoritesDynamic
/**
 * @module favoritesDynamic 收藏动态 /method/favoritesDynamic POST
 * @param id	是	string	动态id
 * */
interface favoritesDynamicData {
	dynamicsId: string,
	userId: string
}
export const favoritesDynamic = (data:favoritesDynamicData) => {
	return request("/app-api/album/dynamics-collect/create", "POST", data)
}

export const deleteFavoritesDynamic = (data:favoritesDynamicData) => {
	return request("/app-api/album/dynamics-collect/delete", "DELETE", data)
}


/**
 * @module dynamicRefresh 刷新动态 /method/dynamicRefresh POST
 * @param dynamic_id	是	string	动态id
 * */
interface dynamicRefreshData {
	dynamic_id: string
}
export const dynamicRefresh = (data:dynamicRefreshData) => {
	return request("/method/dynamicRefresh", "POST", data)
}

/**
 * @module dynamicBatchOperate 动态批量操作 /method/dynamicBatchOperate POST
 * @param status	是	string	状态 2:下架 1:上架
 * @param ids	是	string	动态id
 */
interface dynamicBatchOperateData {
	status: string,
	ids: string
}
export const dynamicBatchOperate = (data:dynamicBatchOperateData) => {
	return request("/app-api/album/dynamics/update-status?status=" + data.status + "&ids=" + data.ids, "PUT")
}

/**
 * @module getShopHomeDynamicList 获取店铺主页动态列表 /app-api/album/dynamics/list GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param isListed	是	number	类型 1:已上架 2:已下架
 */
interface getShopHomeDynamicListData {
	pageNo: number,
	pageSize: number,
	isListed?: number,
	userId?: string
}
export const getShopHomeDynamicList = (data:getShopHomeDynamicListData) => {
	return request("/app-api/album/dynamics/page", "GET", data)
}

/**
 *  根据标签获取动态列表
 */
export const getTagByDynamicList = (data) => {
	return request("/app-api/album/dynamics/get-label-by-dynamics", "GET", data)
}

/**
 *  加入购物车
 */
export const addCart = (data) => {
	return request("/app-api/album/shopping-cart/add", "POST", data)
}

/**
 *  获取购物车列表
 */
export const getCartList = (data) => {
	return request("/app-api/album/shopping-cart/list", "GET", data)
}




/**
 * @module updateDynamicData
 * @param id	是	string	动态id
 * @param isTop	是	string	置顶状态 0:取消置顶 1:置顶
 * @param isListed	是否上架(1上架 2下架)
 * */
interface updateDynamicData {
	id: string,
	isTop?: number,
	isListed?: number
}
export const updateDynamic = (data) => {
	return request("/app-api/album/dynamics/update", "PUT", data)
}

/**
 * @module releaseAlbum 发布动态 /app-api/album/dynamics/create POST
 * @param content	是	string	动态内容
 * @param price	否	number	价格
 * @param pictures	否	string	动态图片 多个逗号隔开
 * */
interface releaseAlbumData {
	content?: string,
	price?: number,
	pictures?: string,
}
export const releaseAlbum = (data:releaseAlbumData) => {
	return request('/app-api/album/dynamics/create', "POST", data)
}

/**
 * @module dynamicDetails 动态详情 /app-api/album/dynamics/detail GET
 * @param id	是	string	动态id
 * */
interface dynamicDetailsData {
	id: string
}
export const dynamicDetails = (data:dynamicDetailsData) => {
	return request("/app-api/album/dynamics/get", "GET", data)
}
// /**
//  * @module getFreightTemplateList 获取运费模板列表 /app-api/album/freight-template/list GET
//  * */
// export const getFreightTemplateList = () => {
// 	return request("/app-api/album/freight-template/list", "GET")
// }


/**
 * @module dynamicForward 动态转发 /app-api/album/dynamics/forward POST
 * @param dynamic_title	是	string	动态内容
 * @param price	否	number	价格
 * @param label_ids	否	string	标签id 多个逗号隔开
 * @param label_names 否 string 标签名称 多个逗号隔开
 * @param size	否	string	规格 多个逗号隔开
 * @param color	否	string	颜色 多个逗号隔开
 * @param freight_template_id	否	string	运费模版id
 * @param goods_short_name	否	string	商品简称
 * @param source_type 否 number 来源类型 1:我的相册 2:其他用户
 * @param source_id 否 string 来源用户id
 * */
interface dynamicForwardData {
	dynamic_title: string,
	price?: number,
	label_ids?: string,
	label_names?: string,
	size?: string,
	color?: string,
	freight_template_id?: string,
	goods_short_name?: string,
    source_type?: number,
	source_id?: string
}
export const dynamicForward = (data:dynamicForwardData, files?: any) => {
	return requestUploadBlobFiles('/app-api/album/dynamics/forward', data, files)
}



/**
 * @module editUser 编辑资料 /method/editUser POST
 * @param mobile	否	string	手机号 
 * @param wechatNumber	否	string	微信 
 * @param nickname	否	string	昵称 
 * @param personalProfile	否	string	个性签名
 * */
interface editUserData {
	mobile?: string,
	wechatNumber?: string,
	nickname?: string,
	personalProfile?: string
}
export const editUser = (data:editUserData) => {
	return request("/app-api/member/user/update", "PUT", data)
}


/**
 * @module uploadFile 上传文件 /app-api/infra/file/upload POST
 * @param files 
 * @returns 
 */
export const uploadFile = (files) => {
	return requestUploadBlobFiles('/app-api/infra/file/upload', {}, files)
}


export const uploadSingleFile = (file) => {
	return requestUploadFile('/app-api/infra/file/upload', file)
}

/**
 * @module getFansList 获取粉丝列表 /fans-list GET
 * @param page	否	number	页码，默认1
 * @param pageSize	否	number	每页条数，默认20
 * @param userId	是	number	用户ID
 * */
interface getFansListData {
	page?: number;
	pageSize?: number;
	userId?: number;
}
export const getFansList = (data?: getFansListData) => {
	return request("/app-api/album/user-follow/fans-list", "GET", data)
}

/**
 * @module uploadHeadImg 上传头像 /method/uploadHeadImg POST
 * */
 export const uploadHeadImg = (files) => {
	return requestUploadBlobFiles('ppxc/method/uploadHeadImg', {}, files)
}

/**
 * @module uploadWxCodeImg 上传微信二维码 /method/uploadWxCodeImg POST
 * */
 export const uploadWxCodeImg = (files) => {
	return requestUploadBlobFiles('ppxc/method/uploadWxCodeImg', {}, files)
}

/** 
 *@module uploadBgImg 上传背景 /method/uploadBgImg POST
 */
export const uploadBgImg = (files) => {
	return requestUploadBlobFiles('ppxc/method/uploadBgImg', {}, files)
}

/**
 * @module bindingPhone 绑定手机号 /method/bindingPhone POST
 * @param phone	是	string	手机号
 * @param unionid	是	string	微信返回的unionid
 * @param code 是	string	验证码
 * */
interface bindingPhoneData {
	phone: string,
	unionid: string,
	code: string
}
export const bindingPhone = (data:bindingPhoneData) => {
	return request("/method/bindingPhone", "POST", data)
}

/**
 * @module getSmsCode 获取短信验证码 /method/getSmsCode GET
 * @param mobile	是	string	手机号
 * @param scene	是	string	短信类型 1:手机号登陆 2:修改手机 3:修改密码 4忘记密码
 * */
interface getSmsCodeData {
	mobile: string,
	scene: number
}
export const getSmsCode = (data:getSmsCodeData) => {
	return request("/app-api/member/auth/send-sms-code", "POST", data)
}

/**
 * @module validateSmsCode 验证短信验证码 /method/validateSmsCode POST
 * @param mobile	是	string	手机号
 * @param scene	是	string	短信类型 1:手机号登陆 2:修改手机 3:修改密码 4忘记密码
 * @param code	是	string	验证码
 * */
interface validateSmsCodeData {
	mobile: string,
	scene: number,
	code: string
}
export const validateSmsCode = (data:validateSmsCodeData) => {
	return request("/app-api/member/auth/validate-sms-code", "POST", data)
}

/**
 * @module replacePhone 更换手机号 /api/method/replacePhone  POST
 * @param phone	否	string	手机号 (第一步旧手机验证)
 * @param code	否	string	验证码 (和phone 配套出现)
 * @param new_phone	否	string	更换的新手机号(第二步新手机验证)
 * @param new_code	否	string	新手机的验证码 (new_phone 配套出现)
 */
interface replacePhoneData {
	phone?: string,
	code?: string,
	new_phone?: string,
	new_code?: string
}
export const replacePhone = (data:replacePhoneData) => {
	return request("/api/method/replacePhone", "POST", data)
}


/**
 * @module changePassword 修改密码 /method/changePassword POST
 * @param phone	是	string	手机号
 * @param password	是	string	密码
 * @param repeat_password	是	string	重复
 * @param code	是	string	验证码
 * */

interface changePasswordData {
	password: string,
	code: string
}
export const changePassword = (data:changePasswordData) => {
	return request("/app-api/member/user/update-password", "PUT", data)
}

interface resetPasswordData {
	password: string,
	code: string,
	mobile: string
}
export const resetPassword = (data:resetPasswordData) => {
	return request("/app-api/member/user/reset-password", "PUT", data)
}

/**
 * @module getAgreement 获取协议 /method/getAgreement GET
 * @param id	是	string	协议id
 * */
interface getAgreementData {
	id: string
}
export const getAgreement = (data:getAgreementData) => {
	return request("/method/getAgreement", "GET", data)
}


/**
 * @module getFriendsList 获取好友列表 /method/getAttentionBusinessList GET
 * @param page	否	string	第几页
 * @param limit	否	string	每页几条
 * */
interface getFriendsListData {	
	page?: number,
	limit?: number,
	nickname?: string
}
export const getFriendsList = (data:getFriendsListData) => {
	return request("/method/getAttentionBusinessList", "GET", data)
}

/**
 * @module getOrderNum 获取订单统计数据 /method/getOrderCount GET
 * */

export const getOrderNum = () => {
	return request("/method/getOrderCount", "GET")
}

/**
 * @module getOrderList 获取订单列表 /method/getOrderCount GET
 * */

export const getOrderList = (data) => {
	return request("/app-api/album/user-order/page", "GET",data)
}

/**
 * @module getShopHome 获取店铺首页 /method/getShopHome GET
 * @param user_id	是	string	用户id
 * */
interface getShopHomeData {
	user_id: string
}
export const getShopHome = (data:getShopHomeData) => {
	return request("/method/getShopHome", "GET", data)
}



/**
 * @module createTag 新建标签 /app-api/album/dynamics/create POST
 * @param name	是	string	标签名称
 * @param user_id 是 string 用户id
 */
interface createTagData {
	name?: string,
	userId?: number,
	type: number

}
export const createTag = (data:createTagData) => {
	return request('/app-api/album/label-and-catalogue/create', "POST", data)
}


/**
 * @module editTag 编辑标签 /app-api/album/dynamics/create POST
 * @param id	是	string	标签名称
 */
interface editTagData {
	id?: string,
}
export const editTag = (data:editTagData) => {
	return request('/app-api/album/label-and-catalogue/update', "PUT", data)
}

/**
 * @module getTagList 获取标签列表 /app-api/album/tag/list GET
 * @param user_id 是 string 用户id
 * @param type 是 number 类型 1:我的标签 2:其他用户标签
 */
interface getTagListData {
	userId: string,
	type: number
}
export const getTagList = (data:getTagListData) => {
	return request('/app-api/album/label-and-catalogue/list', 'GET',data);
}



/**
 * @module getTreeCatalog 获取树目录 GET
 * */

export const getTreeCatalog = (data) => {
	return request("/app-api/album/label-and-catalogue/tree", "GET",data)
}



/**
 * @module getTagDetail 获取标签详情 /app-api/album/label-and-catalogue/detail GET
 * @param id 是 string 标签id
 */
interface getTagDetailData {
	id: string
}
export const getTagDetail = (data:getTagDetailData) => {
	return request('/app-api/album/label-and-catalogue/get', 'GET',data);
}	



/**
 * @module delTag 删除标签 /app-api/album/label-and-catalogue/detail GET
 * @param id 是 string 标签id
 */
interface delTagData {
	id: string
}
export const delTag = (data:delTagData) => {
	return request('/app-api/album/label-and-catalogue/delete?id=' + data.id, 'DELETE');
}	


/**
 * @module getCollectLinkList 获取采集链接列表/app-api/album/label-and-catalogue/detail GET
 * @param url 是 string 输入的短链
 */
interface getCollectLinkListData {
	pageNO? : string
	pageSize? : string
}
export const getCollectLinkList = (data:getCollectLinkListData) => {
	return request('/app-api/album/material-relocation/page', 'GET',data);
}	


/**
 * @module createCollectLink 创建采集链接 /app-api/album/tag/list GET
 * @param url 是 string 输入的短链
 */
interface getCollectDynamicData {
	url: string,
}
export const createCollectLink = (data:getCollectDynamicData) => {
	return request('/app-api/album/material-relocation/create', 'POST',data);
}


/**
 * @module delCollectLink 删除采集链接 /app-api/album/tag/list GET
 * @param url 是 string 输入的短链
 */
interface delCollectLinkData {
	id: string,
}
export const delCollectLink = (data:delCollectLinkData) => {
	return request('/app-api/album/material-relocation/delete?id='+data.id, 'DELETE',data);
}

/**
 * @module getCollectDynamicList 获取动态采集列表 /app-api/album/tag/list GET
 * @param user_id 是 string 用户id
 * @param type 是 number 类型 1:我的标签 2:其他用户标签
 */
interface getCollectDynamicData {
	id: string,
}
export const getCollectDynamicList = (data:getCollectDynamicData) => {
	return request('/app-api/album/material-relocation/get', 'GET',data);
}


/**
 * @module moveHouseDynamic 搬家动态生成新的动态 
 * @param url 是 string 输入的短链
 */

export const moveHouseDynamic = (data:any) => {
	return request('/app-api/album/dynamics/batch-create', 'POST',data);
	//return request('https://dxshy.duxieshe.com/api/task/test', 'POST',data);
	
}


/**
 * @module getLinkId 搬获得已收藏的素材 ID
 */
export const getLinkId = (data:any) => {
	return request('/app-api/album/material-relocation/getAlreadyCollectedId', 'GET',data);
	//return request('https://dxshy.duxieshe.com/api/task/test', 'POST',data);
	
}


/**
 * @module getFormat 搬获商品规格
 */
export const getFormat = (data:any) => {
	return request('/app-api/album/product-specifications/page', 'GET',data);

}

/**
 * @module createFormat 创建商品规格
 */
export const createFormat = (data:any) => {
	return request('/app-api/album/product-specifications/create', 'POST',data);

}

/**
 * @module delFormat 删除商品规格
 */
export const delFormat = (data:any) => {
	return request('/app-api/album/product-specifications/delete?id=' + data.id, "DELETE");

}

/**
 * @module editFormat 编辑商品规格
 */
export const editFormat = (data:any) => {
	return request('/app-api/album/product-specifications/update', 'PUT',data);

}


/**
 * @module getColor 搬获商品颜色
 */
export const getColor = (data:any) => {
	return request('/app-api/album/product-color/page', 'GET',data);
	
}


/**
 * @module createColor 创建商品颜色
 */
export const createColor = (data:any) => {
	return request('/app-api/album/product-color/create', 'POST',data);

}

/**
 * @module delColor 删除商品颜色
 */
export const delColor = (data:any) => {
	return request('/app-api/album/product-color/delete?id=' + data.id, "DELETE");

}

/**
 * @module editColor 编辑商品颜色
 */
export const editColor = (data:any) => {
	return request('/app-api/album/product-color/update', 'PUT',data);

}

/**
 * @module followUser 关注用户 /method/followUser POST
 * @param user_id	是	string	用户id
 * */
interface followUserData {
	userId: string
	followId: string
	isMutual?: number
}
export const followUser = (data:followUserData) => {
	return request("/app-api/album/user-follow/create", "POST", data)
}

/**
 * @module getFollowUserInfo 获取关注用户信息 /app-api/album/user-follow/getUserInfo GET
 * @param user_id	是	string	用户id
 * */
interface getFollowUserInfoData {
	userId: string
}
export const getFollowUserInfo = (data:getFollowUserInfoData) => {
	return request("/app-api/album/user-follow/getUserInfo", "GET", data)
}

interface getSocailAuthRedirectData {
	type: string
	redirectUri: string
}

export const getSocailAuthRedirect = (data: getSocailAuthRedirectData) => {
	return request("/app-api/member/auth/social-auth-redirect", "GET", data)
}

/**
 * @module getDeliveryTemplateList 获取配送模板列表 /app-api/album/delivery-template/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getDeliveryTemplateData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}

export const getDeliveryTemplateList = (data: getDeliveryTemplateData) => {
	return request("/app-api/album/delivery-template/page", "GET", data)
}


/**
 * @module addAddress 添加收货地址 /app-api/album/delivery-template/page POST
 * @param data 
 */
export const addAddress = (data) => {
	return request("/app-api/album/receiving-address/create", "POST", data)
}


/**
 * @module getDeliveryList 获取配送列表 /app-api/album/delivery/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getDeliveryData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}

export const getDeliveryList = (data: getDeliveryData) => {
	return request("/app-api/album/delivery/page", "GET", data)
}

/**
 * @module updateDelivery 更新配送 /app-api/album/delivery/update PUT
 * @param id	是	number	配送id
 * @param userId	是	number	用户id
 * @param deliveryTemplateId	是	number	配送模板id
 * @param orderReminder	是	string	订单提醒
 * @param isEnable	是	number	是否启用
 */
interface updateDeliveryData {
	id?: number,
	userId?: number,
	deliveryTemplateId?: number,
	orderReminder?: string,
	isEnable?: number
}
export const updateDelivery = (data: updateDeliveryData) => {
	return request("/app-api/album/delivery/update", "PUT", data)
}

/**
 * @module createDelivery 创建配送 /app-api/album/delivery/create POST
 * @param userId	是	number	用户id
 * @param deliveryTemplateId	是	number	配送模板id
 * @param orderReminder	是	string	订单提醒
 * @param isEnable	是	number	是否启用
 */
interface createDeliveryData {
	userId?: number,
	deliveryTemplateId?: number,
	orderReminder?: string,
	isEnable?: number
}
export const createDelivery = (data: createDeliveryData) => {
	return request("/app-api/album/delivery/create", "POST", data)
}


/**
 * @module getFreightRulesBillingList 获取运费规则列表 /app-api/album/freight-rules-billing/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getFreightRulesBillingListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getFreightRulesBillingList = (data: getFreightRulesBillingListData) => {
	return request("/app-api/album/freight-rules-billing/page", "GET", data)
}


/**
 * @module updateFreightRulesBilling 更新运费规则计费方式 /app-api/album/freight-rules-billing/update PUT
 * @param id	是	number	规则id
 * @param userId	是	number	用户id
 * @param type	是	number	计费方式
 */
interface updateFreightRulesBillingData {
	id: number,
	userId: number,
	type: number
}
export const updateFreightRulesBilling = (data: updateFreightRulesBillingData) => {
	return request("/app-api/album/freight-rules-billing/update", "PUT", data)
}

/**
 * @module createFreightRulesBilling 创建运费规则计费方式 /app-api/album/freight-rules-billing/create POST
 * @param userId	是	number	用户id
 * @param type	是	number	计费方式
 */
interface createFreightRulesBillingData {
	userId: number,
	type: number
}
export const createFreightRulesBilling = (data: createFreightRulesBillingData) => {
	return request("/app-api/album/freight-rules-billing/create", "POST", data)
}


/**
 * @module getFreightTemplateList 获取运费模板列表 /app-api/album/freight-template/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getFreightTemplateListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getFreightTemplateList = (data: getFreightTemplateListData) => {
	return request("/app-api/album/freight-template/page", "GET", data)
}


/**
 * @module getFreightTemplateAll 获取运费模板列表 /app-api/album/freight-template/all
 * @param userId	是	number	用户id
 */
interface getFreightTemplateAllData {
	userId: number
}
export const getFreightTemplateAll = (data: getFreightTemplateAllData) => {
	return request("/app-api/album/freight-template/all", "GET", data)
}


/**
 * @module createFreightTemplate 创建运费模板 /app-api/album/freight-template/create POST
 * @param userId	是	number	用户id
 * @param name	是	string	模板名称
 * @param nonDeliveryArea	是	string	不配送区域
 * @param isDefault	是	number	是否默认
 */
interface createFreightTemplateData {
	userId: number,
	name: string,
	nonDeliveryArea: string,
	isDefault: number
}
export const createFreightTemplate = (data: createFreightTemplateData) => {
	return request("/app-api/album/freight-template/create", "POST", data)
}


/**
 * @module updateFreightTemplate 更新运费模板 /app-api/album/freight-template/update PUT
 * @param id	是	number	模板id
 * @param userId	是	number	用户id
 * @param name	是	string	模板名称
 * @param nonDeliveryArea	是	string	不配送区域
 * @param isDefault	是	number	是否默认
 */
interface updateFreightTemplateData {
	id: number,
	userId: number,
	name?: string,
	nonDeliveryArea?: string,
	isDefault?: number
}
export const updateFreightTemplate = (data: updateFreightTemplateData) => {
	return request("/app-api/album/freight-template/update", "PUT", data)
}

/**
 * @module deleteFreightTemplate 删除运费模板 /app-api/album/freight-template/delete DELETE
 * @param id	是	number	模板id
 */
interface deleteFreightTemplateData {
	id: number
}
export const deleteFreightTemplate = (data: deleteFreightTemplateData) => {
	return request("/app-api/album/freight-template/delete?id=" + data.id, "DELETE")
}

/**
 * @module createAndFreightRules 创建运费规则 /app-api/album/freight-template/creatAndFreightRules POST
 * @param appFreightTemplateSaveReqVO	是	object	模板信息
 * @param appFreightRulesSaveReqVOList	是	object	规则信息
 */
interface FreightRulesSaveReqVO {
	userId: number,
	area: string,
	freightType: number,
	freightAmount: number,
	isFreeShippingConditions: number,
	freeShippingAmount?: number
  }
  
interface createAndFreightRulesData {
	appFreightTemplateSaveReqVO: {
	  userId: number,
	  name: string,
	  nonDeliveryArea?: string,
	  isDefault?: number
	},
	appFreightRulesSaveReqVOList?: FreightRulesSaveReqVO[]
  }
export const createAndFreightRules = (data: createAndFreightRulesData) => {
	return request("/app-api/album/freight-template/creatAndFreightRules", "POST", data)
}


/**
 * @module createFreightRules 创建运费规则 /app-api/album/freight-rules/create POST
 * @param userId	是	number	用户id
 * @param area	是	string	区域
 * @param freightTemplateId	是	number	模板id
 * @param freightType	是	number	运费类型
 * @param freightAmount	是	number	运费金额
 * @param isFreeShippingConditions	是	number	是否包邮条件
 * @param freeShippingAmount	是	number	包邮金额
 */
interface createFreightRulesData {
	userId: number,
	area: string,
	freightTemplateId: number,
	freightType: number,
	freightAmount: number,
	isFreeShippingConditions: number,
	freeShippingAmount: number
}
export const createFreightRules = (data: createFreightRulesData) => {
	return request("/app-api/album/freight-rules/create", "POST", data)
}

/**
 * @module updateFreightRules 更新运费规则 /app-api/album/freight-rules/update PUT
 * @param id	是	number	规则id
 * @param userId	是	number	用户id
 * @param area	是	string	区域
 * @param freightTemplateId	是	number	模板id
 */
interface updateFreightRulesData {
	id: number,
	userId: number,
	area: string,
	freightTemplateId: number,
	freightType: number,
	freightAmount: number,
	isFreeShippingConditions: number,
	freeShippingAmount: number
}
export const updateFreightRules = (data: updateFreightRulesData) => {
	return request("/app-api/album/freight-rules/update", "PUT", data)
}

/**
 * @module deleteFreightRules 删除运费规则 /app-api/album/freight-rules/delete DELETE
 * @param id	是	number	规则id
 */
interface deleteFreightRulesData {
	id: number
}
export const deleteFreightRules = (data: deleteFreightRulesData) => {
	return request("/app-api/album/freight-rules/delete", "DELETE", data)
}

// ================================== 会员中心相关接口 ==================================
/**
 * @module getVipOrderList 获取会员已购服务列表 /app-api/member/vip/orders GET
 * @param page	否	number	页码，默认1
 * @param limit	否	number	每页数量，默认10
 */
interface GetVipOrderListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number,
	payStatus?: number
}
export const getVipOrderList = (data?: GetVipOrderListData) => {
	return request("/app-api/album/member-order-activation/page", "GET", data) // todo
}

/**
 * @module createVipOrder 创建会员订单 /app-api/album/member-order-activation/create POST
 * @param memberType	是	number	会员类型(1半年 2一年)
 * @param userId	否	number	用户编号
 * @param price	否	number	价格，单位：分
 * @param payStatus	否	boolean	是否已支付：[0:未支付 1:已经支付过]
 * @param payOrderId	否	number	支付订单编号
 * @param payChannelCode	否	string	支付成功的支付渠道
 * @param payTime	否	string	订单支付时间
 * @param payRefundId	否	number	退款订单编号
 * @param refundPrice	否	number	退款金额，单位：分
 * @param refundTime	否	string	退款时间
 * @param transferChannelPackageInfo	否	string	渠道 package 信息
 */
interface CreateVipOrderData {
    menberType: number, //后端使用menberType（应该为：memberType ）
    userId?: number,
    price?: number,
    payStatus?: number,
    payOrderId?: number,
    payChannelCode?: string,
    payTime?: string,
    payRefundId?: number,
    refundPrice?: number,
    refundTime?: string,
    transferChannelPackageInfo?: string
}

export const createVipOrder = (data: CreateVipOrderData) => {
    return request("/app-api/album/member-order-activation/create", "POST", data)
}

/**
 * @module getPayOrder 获取支付订单 /app-api/pay/order/get GET
 * @param id 是 number 支付单id
 */
interface GetPayOrderData {
    id: number
}
export const getPayOrder = (data: GetPayOrderData) => {
    return request("/app-api/pay/order/get", "GET", data)
}

/**
 * @module submitPayOrder 提交支付订单 /app-api/pay/order/submit POST
 * @param id 是 number 支付单id
 * @param channelCode 是 string 支付渠道编码
 * @param channelExtras 否 object 支付渠道额外参数
 * @param displayMode 是 string 展示模式
 * @param returnUrl 否 string 支付成功返回地址
 */
interface SubmitPayOrderData {
    id: number,
    channelCode: string,
    channelExtras?: object,
    displayMode: string,
    returnUrl?: string
}
export const submitPayOrder = (data: SubmitPayOrderData) => {
    return request("/app-api/pay/order/submit", "POST", data)
}

/**
 * @module getTransactionList 获取交易明细列表 /app-api/album/income-expenditure-detail/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getTransactionListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getTransactionList= (data: getTransactionListData) => {
	return request("/app-api/album/income-expenditure-detail/page", "GET", data)
}

/**
 * @module getWalletList 获取平台钱包列表 /app-api/album/platform-wallet/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getWalletListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getWalletList= (data: getWalletListData) => {
	return request("/app-api/album/platform-wallet/page", "GET", data)
}

/**
 * @module getWithdrawalList 获取提现记录列表 /app-api/album/withdrawal/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getWithdrawalListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getWithdrawalList = (data: getWithdrawalListData) => {
	return request("/app-api/album/withdrawal/page", "GET", data)
}

// ================================== AI 对话模块相关接口 ==================================

/**
 * @module createAiChatConversation 创建【我的】聊天对话 /app-api/ai/chat/conversation/create-my POST
 * @param roleId	否	number	聊天角色编号
 * @param knowledgeId	否	number	知识库编号
 */
interface createAiChatConversationData {
	roleId?: number,
	knowledgeId?: number
}
export const createAiChatConversation = (data?: createAiChatConversationData) => {
	return request("/app-api/ai/chat/conversation/create-my", "POST", data || {})
}

/**
 * @module getAiChatConversationList 获得【我的】聊天对话列表 /app-api/ai/chat/conversation/my-list GET
 */
export const getAiChatConversationList = () => {
	return request("/app-api/ai/chat/conversation/my-list", "GET")
}

/**
 * @module updateAiChatConversation 更新【我的】聊天对话 /app-api/ai/chat/conversation/update-my PUT
 * @param id	是	number	对话编号
 * @param title	否	string	对话标题
 * @param pinned	否	boolean	是否置顶
 * @param modelId	否	number	模型编号
 * @param knowledgeId	否	number	知识库编号
 * @param systemMessage	否	string	系统消息
 * @param temperature	否	number	温度参数
 * @param maxTokens	否	number	最大令牌数
 * @param maxContexts	否	number	最大上下文数
 */
interface updateAiChatConversationData {
	id: number,
	title?: string,
	pinned?: boolean,
	modelId?: number,
	knowledgeId?: number,
	systemMessage?: string,
	temperature?: number,
	maxTokens?: number,
	maxContexts?: number
}
export const updateAiChatConversation = (data: updateAiChatConversationData) => {
	return request("/app-api/ai/chat/conversation/update-my", "PUT", data)
}

/**
 * @module deleteAiChatConversation 删除聊天对话 /app-api/ai/chat/conversation/delete-my DELETE
 * @param id	是	number	对话编号
 */
interface deleteAiChatConversationData {
	id: number
}
export const deleteAiChatConversation = (data: deleteAiChatConversationData) => {
	return request("/app-api/ai/chat/conversation/delete-my?id=" + data.id, "DELETE")
}

// ================================== AI 聊天消息模块相关接口 ==================================

/**
 * @module sendAiChatMessageStream 发送消息（流式） /app-api/ai/chat/message/send-stream POST
 * @param conversationId	是	string	对话编号
 * @param content	是	string	消息内容
 * @param useContext	否	boolean	是否使用上下文
 */
interface sendAiChatMessageStreamData {
	conversationId: string,
	content: string,
	useContext?: boolean
}
export const sendAiChatMessageStream = (data: sendAiChatMessageStreamData) => {
	// 设置 SSE 流式响应所需的请求头
	const headers = {
		'Accept': 'text/event-stream',
		'Cache-Control': 'no-cache',
		'Connection': 'keep-alive'
	};
	
	return request({
		url: "/app-api/ai/chat/message/send-stream",
		method: "POST",
		data: data,
		headers: headers
	})
}

/**
 * @module getAiChatMessagePage 获得消息分页 /app-api/ai/chat/message/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页条数
 * @param conversationId	是	number	对话编号
 * @param userId	是	number	用户编号
 */
interface getAiChatMessagePageData {
	pageNo: number,
	pageSize: number,
	conversationId: number,
	userId: number
}
export const getAiChatMessagePage = (data: getAiChatMessagePageData) => {
	return request("/app-api/ai/chat/message/page", "GET", data)
}
