// WebView环境测试工具
// 用于在开发和调试阶段测试原生桥接功能

import NativeBridge, { PERMISSION_TYPES, PAY_STATUS, PermissionInfo, WxUserInfo } from './nativeBridge';

// 测试用的模拟数据
const TEST_DATA = {
  // 测试用的1x1像素透明PNG图片
  QR_IMAGE: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
  
  // 测试用的Base64图片（简单的红色方块）
  RED_SQUARE: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAFJBuQjXQAAAABJRU5ErkJggg==',
  
  // 测试用的支付信息
  PAY_INFO: JSON.stringify({
    appId: 'test_app_id',
    timeStamp: Date.now().toString(),
    nonceStr: 'test_nonce_' + Math.random(),
    package: 'prepay_id=test_prepay_id',
    signType: 'MD5',
    paySign: 'test_sign'
  }),
  
  // 测试用的用户ID
  USER_ID: 'test_user_123',
  DYNAMIC_ID: 'test_dynamic_456'
};

export class WebViewTester {

  // ==================== 环境检测测试 ====================

  /**
   * 测试环境检测功能
   */
  static testEnvironmentDetection() {
    console.group('🔍 环境检测测试');

    console.log('当前环境信息:');
    console.log('- User Agent:', navigator.userAgent);
    console.log('- Platform:', navigator.platform);
    console.log('- Language:', navigator.language);
    console.log('- 是否在WebView中:', NativeBridge.isInWebView());
    console.log('- 是否为Android WebView:', NativeBridge.isAndroidWebView());
    console.log('- 是否为iOS WebView:', NativeBridge.isIOSWebView());

    // 检查全局对象
    console.log('\n全局对象检测:');
    const globalObjects = {
      'exitApp': !!window.exitApp,
      'isOtherApp': !!window.isOtherApp,
      'acceptPrivateAgreement': !!window.acceptPrivateAgreement,
      'openWx': !!window.openWx,
      'wxLogin': !!window.wxLogin,
      'wxPay': !!window.wxPay,
      'shareImgWx': !!window.shareImgWx,
      'shareImgWxFavorite': !!window.shareImgWxFavorite,
      'shareBase64ImgWx': !!window.shareBase64ImgWx,
      'shareBase64ImgWxCircle': !!window.shareBase64ImgWxCircle,
      'downloadImg': !!window.downloadImg,
      'downloadBase64Img': !!window.downloadBase64Img,
      'downloadVideo': !!window.downloadVideo,
      'downloadFile': !!window.downloadFile,
      'aliPay': !!window.aliPay,
      'checkPermission': !!window.checkPermission,
      'requestPermission': !!window.requestPermission,
      'openSetting': !!window.openSetting,
      'StatusBarDarkMode': !!window.StatusBarDarkMode,
      'StatusBarLightMode': !!window.StatusBarLightMode,
      'setPhotoNum': !!window.setPhotoNum,
      'openMiniProgramTrend': !!window.openMiniProgramTrend,
      'openMiniProgramUser': !!window.openMiniProgramUser,
      'webkit': !!window.webkit,
      'webkit.messageHandlers': !!window.webkit?.messageHandlers
    };

    Object.entries(globalObjects).forEach(([key, exists]) => {
      console.log(`- window.${key}:`, exists ? '✅' : '❌');
    });

    console.groupEnd();
    return globalObjects;
  }

  // ==================== 应用控制类测试 ====================

  /**
   * 测试应用控制功能
   */
  static testAppControl() {
    console.group('🏠 应用控制测试');

    try {
      console.log('测试检测第三方应用...');
      NativeBridge.isOtherApp();
      console.log('✅ isOtherApp 调用成功');

      console.log('\n测试接受隐私协议...');
      NativeBridge.acceptPrivateAgreement();
      console.log('✅ acceptPrivateAgreement 调用成功');

      console.log('\n⚠️ exitApp 需要手动测试，可能会关闭应用');
      console.log('如需测试退出应用，请手动调用: NativeBridge.exitApp()');

      return true;
    } catch (error) {
      console.error('❌ 应用控制测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 微信功能测试 ====================

  /**
   * 测试微信登录
   */
  static async testWechatLogin() {
    console.group('🔐 微信登录测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('微信登录仅在WebView环境中可用');
      }

      console.log('开始微信登录测试...');
      const userInfo = await NativeBridge.wxLogin();
      console.log('✅ 微信登录成功:', userInfo);
      
      // 验证返回数据
      if (userInfo.unionid && userInfo.nickname) {
        console.log('✅ 用户信息完整');
        return userInfo;
      } else {
        console.warn('⚠️ 用户信息不完整');
        return userInfo;
      }
    } catch (error) {
      console.error('❌ 微信登录失败:', error);
      return null;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 测试微信支付
   */
  static async testWechatPay() {
    console.group('💰 微信支付测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('微信支付仅在WebView环境中可用');
      }

      console.log('开始微信支付测试...');
      console.log('支付信息:', TEST_DATA.PAY_INFO);
      
      const payResult = await NativeBridge.wxPay(TEST_DATA.PAY_INFO);
      console.log('支付结果:', payResult);
      
      switch (payResult) {
        case PAY_STATUS.SUCCESS:
          console.log('✅ 微信支付成功');
          return true;
        case PAY_STATUS.FAILED:
          console.log('❌ 微信支付失败');
          return false;
        case PAY_STATUS.CANCELLED:
          console.log('⚠️ 用户取消支付');
          return false;
        case PAY_STATUS.PENDING:
          console.log('⏳ 支付等待确认');
          return false;
        default:
          console.log('❓ 未知支付状态:', payResult);
          return false;
      }
    } catch (error) {
      console.error('❌ 微信支付测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 测试微信分享功能
   */
  static testWechatShare() {
    console.group('🔗 微信分享测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('微信分享仅在WebView环境中可用');
      }

      console.log('测试分享Base64图片到微信好友...');
      NativeBridge.shareBase64ImageToWechat(TEST_DATA.QR_IMAGE);
      console.log('✅ shareBase64ImageToWechat 调用成功');

      console.log('\n测试分享Base64图片到朋友圈...');
      NativeBridge.shareBase64ImageToWechatCircle(TEST_DATA.RED_SQUARE);
      console.log('✅ shareBase64ImageToWechatCircle 调用成功');

      console.log('\n测试分享图片URL到微信好友...');
      NativeBridge.shareImageToWechat('https://example.com/test.jpg');
      console.log('✅ shareImageToWechat 调用成功');

      console.log('\n测试分享图片URL到微信收藏...');
      NativeBridge.shareImageToWechatFavorite('https://example.com/test.jpg');
      console.log('✅ shareImageToWechatFavorite 调用成功');

      console.log('\n测试打开微信...');
      NativeBridge.openWechat();
      console.log('✅ openWechat 调用成功');

      return true;
    } catch (error) {
      console.error('❌ 微信分享测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 支付功能测试 ====================

  /**
   * 测试支付宝支付
   */
  static async testAliPay() {
    console.group('💳 支付宝支付测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('支付宝支付仅在WebView环境中可用');
      }

      console.log('开始支付宝支付测试...');
      console.log('支付信息:', TEST_DATA.PAY_INFO);
      
      const payResult = await NativeBridge.aliPay(TEST_DATA.PAY_INFO);
      console.log('支付结果:', payResult);
      
      if (payResult === PAY_STATUS.SUCCESS) {
        console.log('✅ 支付宝支付成功');
        return true;
      } else {
        console.log('❌ 支付宝支付失败，状态:', payResult);
        return false;
      }
    } catch (error) {
      console.error('❌ 支付宝支付测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 文件下载测试 ====================

  /**
   * 测试图片下载和保存功能
   */
  static async testImageDownload() {
    console.group('📥 图片下载测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('图片下载仅在WebView环境中可用');
      }

      // 测试URL图片下载
      console.log('测试下载URL图片...');
      await NativeBridge.downloadImage('https://picsum.photos/200/200');
      console.log('✅ downloadImage 成功');

      // 测试Base64图片保存
      console.log('\n测试保存Base64图片...');
      const saveSuccess = await NativeBridge.saveImageToAlbumNative(TEST_DATA.QR_IMAGE);
      console.log('保存结果:', saveSuccess ? '✅ 成功' : '❌ 失败');

      // 测试Base64图片下载
      console.log('\n测试下载Base64图片...');
      NativeBridge.downloadBase64Image(TEST_DATA.RED_SQUARE);
      console.log('✅ downloadBase64Image 调用成功');

      return true;
    } catch (error) {
      console.error('❌ 图片下载测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 测试视频和文件下载
   */
  static async testFileDownload() {
    console.group('📹 文件下载测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('文件下载仅在WebView环境中可用');
      }

      // 测试视频下载
      console.log('测试下载视频文件...');
      await NativeBridge.downloadVideo('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4');
      console.log('✅ downloadVideo 成功');

      // 测试普通文件下载
      console.log('\n测试下载普通文件...');
      await NativeBridge.downloadFile('https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf');
      console.log('✅ downloadFile 成功');

      return true;
    } catch (error) {
      console.error('❌ 文件下载测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 权限管理测试 ====================

  /**
   * 测试权限检查功能
   */
  static async testPermissionCheck() {
    console.group('🔍 权限检查测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('权限检查仅在WebView环境中可用');
      }

      console.log('开始检查所有权限...');
      const permissions = await NativeBridge.checkPermission();
      
      console.log('权限状态详情:');
      const permissionNames = {
        openCamera: '相机',
        openGalleryPhoto: '相册图片',
        openGalleryVideo: '相册视频', 
        openGalleryAudio: '相册音频',
        openStorage: '存储',
        openContacts: '通讯录',
        openLocation: '位置',
        openAudio: '麦克风',
        openNotice: '通知'
      };

      Object.entries(permissions).forEach(([key, value]) => {
        const name = permissionNames[key as keyof PermissionInfo] || key;
        console.log(`- ${name}: ${value ? '✅ 已授权' : '❌ 未授权'}`);
      });

      console.log('✅ 权限检查完成');
      return permissions;
    } catch (error) {
      console.error('❌ 权限检查失败:', error);
      return null;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 测试权限请求功能
   */
  static async testPermissionRequest() {
    console.group('📋 权限请求测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('权限请求仅在WebView环境中可用');
      }

      // 测试请求相机权限
      console.log('测试请求相机权限...');
      const cameraGranted = await NativeBridge.requestPermission('CAMERA');
      console.log('相机权限结果:', cameraGranted ? '✅ 已授权' : '❌ 被拒绝');

      // 测试请求存储权限
      console.log('\n测试请求存储权限...');
      const storageGranted = await NativeBridge.requestPermission('STORAGE');
      console.log('存储权限结果:', storageGranted ? '✅ 已授权' : '❌ 被拒绝');

      // 测试打开设置页面
      console.log('\n测试打开系统设置...');
      NativeBridge.openSetting();
      console.log('✅ openSetting 调用成功');

      return { cameraGranted, storageGranted };
    } catch (error) {
      console.error('❌ 权限请求测试失败:', error);
      return null;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== UI控制测试 ====================

  /**
   * 测试状态栏控制
   */
  static testStatusBarControl() {
    console.group('📱 状态栏控制测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('状态栏控制仅在WebView环境中可用');
      }

      console.log('测试设置状态栏深色模式（黑字）...');
      NativeBridge.setStatusBarDarkMode();
      console.log('✅ setStatusBarDarkMode 调用成功');

      setTimeout(() => {
        console.log('\n测试设置状态栏浅色模式（白字）...');
        NativeBridge.setStatusBarLightMode();
        console.log('✅ setStatusBarLightMode 调用成功');
      }, 2000);

      return true;
    } catch (error) {
      console.error('❌ 状态栏控制测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 测试照片选择器配置
   */
  static testPhotoSelector() {
    console.group('📷 照片选择器测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('照片选择器配置仅在WebView环境中可用');
      }

      console.log('测试设置照片选择数量为5...');
      NativeBridge.setPhotoSelectNum(5);
      console.log('✅ setPhotoSelectNum(5) 调用成功');

      setTimeout(() => {
        console.log('\n测试重置照片选择数量为9...');
        NativeBridge.setPhotoSelectNum(9);
        console.log('✅ setPhotoSelectNum(9) 调用成功');
      }, 1000);

      return true;
    } catch (error) {
      console.error('❌ 照片选择器测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 小程序功能测试 ====================

  /**
   * 测试小程序功能
   */
  static testMiniProgram() {
    console.group('🚀 小程序功能测试');

    try {
      if (!NativeBridge.isInWebView()) {
        throw new Error('小程序功能仅在WebView环境中可用');
      }

      console.log('测试打开小程序用户页面...');
      NativeBridge.openMiniProgramUser(TEST_DATA.USER_ID);
      console.log('✅ openMiniProgramUser 调用成功');

      console.log('\n测试打开小程序动态页面...');
      NativeBridge.openMiniProgramTrend(TEST_DATA.DYNAMIC_ID);
      console.log('✅ openMiniProgramTrend 调用成功');

      return true;
    } catch (error) {
      console.error('❌ 小程序功能测试失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  // ==================== 综合测试 ====================

  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.clear();
    console.log('🚀 开始WebView功能完整测试...\n');
    
    const results: { [key: string]: any } = {};

    // 1. 环境检测
    console.log('🔍 第1步：环境检测');
    results.environment = this.testEnvironmentDetection();
    await this.delay(500);

    // 2. 显示调试信息
    console.log('\n🔧 第2步：调试信息');
    this.showBridgeInfo();
    await this.delay(500);

    // 只在WebView环境中测试原生功能
    if (NativeBridge.isInWebView()) {
      console.log('\n📱 在WebView环境中，继续测试原生功能...\n');
      
      // 3. 应用控制测试
      console.log('🏠 第3步：应用控制功能');
      results.appControl = this.testAppControl();
      await this.delay(1000);

      // 4. 权限测试
      console.log('\n🔐 第4步：权限管理功能');
      results.permissions = await this.testPermissionCheck();
      await this.delay(1000);

      // 5. 微信分享测试
      console.log('\n🔗 第5步：微信分享功能');
      results.wechatShare = this.testWechatShare();
      await this.delay(1000);

      // 6. 图片下载测试
      console.log('\n📥 第6步：图片下载功能');
      results.imageDownload = await this.testImageDownload().catch(() => false);
      await this.delay(1000);

      // 7. UI控制测试
      console.log('\n📱 第7步：UI控制功能');
      results.statusBar = this.testStatusBarControl();
      results.photoSelector = this.testPhotoSelector();
      await this.delay(1000);

      // 8. 小程序功能测试
      console.log('\n🚀 第8步：小程序功能');
      results.miniProgram = this.testMiniProgram();
      await this.delay(1000);

      console.log('\n⚠️  需要用户交互的测试项：');
      console.log('- 微信登录：WebViewTester.testWechatLogin()');
      console.log('- 微信支付：WebViewTester.testWechatPay()');
      console.log('- 支付宝支付：WebViewTester.testAliPay()');
      console.log('- 权限请求：WebViewTester.testPermissionRequest()');
      console.log('- 文件下载：WebViewTester.testFileDownload()');

    } else {
      console.log('\n⚠️ 不在WebView环境中，跳过原生功能测试');
      console.log('💡 可以调用 WebViewTester.mockNativeBridge() 启用模拟环境');
    }
    
    console.log('\n✅ 所有自动测试完成');
    console.log('📊 测试结果概览:', results);
    
    return results;
  }

  /**
   * 运行需要用户交互的测试
   */
  static async runInteractiveTests() {
    console.log('🎯 开始用户交互测试...\n');
    
    if (!NativeBridge.isInWebView()) {
      console.warn('⚠️ 不在WebView环境中，无法进行交互测试');
      return;
    }

    const results: { [key: string]: any } = {};

    // 权限请求测试
    console.log('🔐 测试权限请求...');
    results.permissionRequest = await this.testPermissionRequest();
    await this.delay(2000);

    // 微信登录测试
    console.log('\n🔐 测试微信登录...');
    results.wechatLogin = await this.testWechatLogin();
    await this.delay(2000);

    // 支付测试（谨慎使用）
    console.log('\n💰 支付测试（仅在测试环境使用）...');
    console.log('⚠️  请确认当前为测试环境再执行支付测试');
    
    console.log('\n✅ 交互测试完成');
    console.log('📊 交互测试结果:', results);
    
    return results;
  }

  // ==================== 模拟环境 ====================

  /**
   * 模拟原生桥接（用于开发调试）
   */
  static mockNativeBridge() {
    if (!NativeBridge.isInWebView()) {
      console.log('🔧 启用原生桥接模拟环境...');

      // 应用控制类
      window.exitApp = {
        exitApp: () => {
          console.log('🚪 [模拟] 退出应用');
          alert('模拟：退出应用');
        }
      };

      window.isOtherApp = {
        isOtherApp: () => {
          console.log('🔍 [模拟] 检测第三方应用');
        }
      };

      window.acceptPrivateAgreement = {
        acceptPrivateAgreement: () => {
          console.log('📋 [模拟] 接受隐私协议');
        }
      };

      // 微信相关功能
      window.openWx = {
        openWx: () => {
          console.log('📱 [模拟] 打开微信');
          alert('模拟：打开微信');
        }
      };

      window.wxLogin = {
        wxLogin: () => {
          console.log('🔐 [模拟] 微信登录');
          // 模拟登录成功回调
          setTimeout(() => {
            window.wxLoginInfo?.({
              nickname: '测试用户',
              head_img: 'https://example.com/avatar.jpg',
              unionid: 'mock_unionid_' + Date.now()
            });
          }, 1500);
        }
      };

      window.wxPay = {
        wxPay: (payInfo: string) => {
          console.log('💰 [模拟] 微信支付:', payInfo);
          // 模拟支付成功
          setTimeout(() => window.webPaySuc?.(PAY_STATUS.SUCCESS), 2000);
        }
      };

      window.shareBase64ImgWx = {
        shareBase64ImgWx: (base64Img: string) => {
          console.log('📤 [模拟] 分享Base64图片到微信好友, 大小:', base64Img.length);
          alert(`模拟：分享Base64图片到微信好友，图片大小: ${base64Img.length} 字符`);
        }
      };

      window.shareBase64ImgWxCircle = {
        shareBase64ImgWxCircle: (base64Img: string) => {
          console.log('📤 [模拟] 分享Base64图片到朋友圈, 大小:', base64Img.length);
          alert(`模拟：分享Base64图片到朋友圈，图片大小: ${base64Img.length} 字符`);
        }
      };

      window.shareImgWx = {
        shareImgWx: (url: string) => {
          console.log('📤 [模拟] 分享图片URL到微信好友:', url);
          alert(`模拟：分享图片到微信好友\n${url}`);
        }
      };

      window.shareImgWxFavorite = {
        shareImgWxFavorite: (url: string) => {
          console.log('📤 [模拟] 分享图片URL到微信收藏:', url);
          alert(`模拟：分享图片到微信收藏\n${url}`);
        }
      };

      // 文件下载
      window.downloadImg = {
        downloadImg: (url: string) => {
          console.log('📥 [模拟] 下载图片:', url);
          setTimeout(() => window.webDownloadSuc?.(), 1000);
        }
      };

      window.downloadBase64Img = {
        downloadBase64Img: (base64Img: string) => {
          console.log('💾 [模拟] 下载Base64图片, 大小:', base64Img.length);
          alert(`模拟：保存图片到相册，图片大小: ${base64Img.length} 字符`);
        }
      };

      window.downloadVideo = {
        downloadVideo: (url: string) => {
          console.log('📹 [模拟] 下载视频:', url);
          setTimeout(() => window.webDownloadSuc?.(), 2000);
        }
      };

      window.downloadFile = {
        downloadFile: (url: string) => {
          console.log('📄 [模拟] 下载文件:', url);
          setTimeout(() => window.webDownloadSuc?.(), 1500);
        }
      };

      // 支付功能
      window.aliPay = {
        aliPay: (payInfo: string) => {
          console.log('💳 [模拟] 支付宝支付:', payInfo);
          // 模拟支付成功
          setTimeout(() => window.webPaySuc?.(PAY_STATUS.SUCCESS), 2000);
        }
      };

      // 权限管理
      window.checkPermission = {
        checkPermission: () => {
          console.log('🔍 [模拟] 检查权限');
          // 模拟权限检查结果
          setTimeout(() => {
            window.checkPermissionCallBack?.({
              openCamera: true,
              openGalleryPhoto: true,
              openGalleryVideo: true,
              openGalleryAudio: false,
              openStorage: true,
              openContacts: false,
              openLocation: false,
              openAudio: true,
              openNotice: true
            });
          }, 800);
        }
      };

      window.requestPermission = {
        requestPermission: (type: string) => {
          console.log('📋 [模拟] 请求权限:', type);
          // 模拟权限授权成功
          setTimeout(() => window.webPermissonConsent?.(), 1500);
        }
      };

      window.openSetting = {
        openSetting: () => {
          console.log('⚙️ [模拟] 打开系统设置');
          alert('模拟：打开系统设置');
        }
      };

      // UI控制
      window.StatusBarDarkMode = {
        StatusBarDarkMode: () => {
          console.log('🌙 [模拟] 设置状态栏深色模式');
        }
      };

      window.StatusBarLightMode = {
        StatusBarLightMode: () => {
          console.log('☀️ [模拟] 设置状态栏浅色模式');
        }
      };

      window.setPhotoNum = {
        setPhotoNum: (num: number) => {
          console.log('📷 [模拟] 设置照片选择数量:', num);
        }
      };

      // 小程序功能
      window.openMiniProgramTrend = {
        openMiniProgramTrend: (dynamicId: string) => {
          console.log('🚀 [模拟] 打开小程序动态页面:', dynamicId);
          alert(`模拟：打开小程序动态页面\nID: ${dynamicId}`);
        }
      };

      window.openMiniProgramUser = {
        openMiniProgramUser: (userId: string) => {
          console.log('🚀 [模拟] 打开小程序用户页面:', userId);
          alert(`模拟：打开小程序用户页面\nID: ${userId}`);
        }
      };

      console.log('✅ 原生桥接模拟环境已启用');
      console.log('💡 现在可以运行所有测试：WebViewTester.runAllTests()');
    } else {
      console.log('ℹ️ 检测到真实的原生桥接环境，无需启用模拟');
    }
  }

  // ==================== 调试工具 ====================

  /**
   * 显示当前桥接配置和调试信息
   */
  static showBridgeInfo() {
    console.group('🔍 桥接配置和调试信息');

    const debugInfo = NativeBridge.getDebugInfo();
    
    console.log('🌍 环境信息:');
    Object.entries(debugInfo.environment).forEach(([key, value]) => {
      console.log(`  ${key}:`, value);
    });

    console.log('\n🔌 可用接口:');
    Object.entries(debugInfo.availableInterfaces).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`  ${key}: ${status}`);
    });

    console.log('\n💾 本地存储:');
    Object.entries(debugInfo.localStorage).forEach(([key, value]) => {
      console.log(`  ${key}:`, value || '(未设置)');
    });

    console.groupEnd();
    return debugInfo;
  }

  /**
   * 获取测试报告
   */
  static generateTestReport() {
    const report = {
      timestamp: new Date().toISOString(),
      environment: this.testEnvironmentDetection(),
      debugInfo: NativeBridge.getDebugInfo(),
      testData: TEST_DATA
    };

    console.log('📊 测试报告生成完成:', report);
    return report;
  }

  /**
   * 清理测试环境
   */
  static cleanup() {
    console.log('🧹 清理测试环境...');
    
    // 清理可能的定时器和回调
    window.wxLoginInfo = undefined;
    window.webPaySuc = undefined;
    window.webDownloadSuc = undefined;
    window.webDownloadFail = undefined;
    window.webPermissonConsent = undefined;
    window.webPermissonDeny = undefined;
    window.checkPermissionCallBack = undefined;
    
    console.log('✅ 测试环境清理完成');
  }

  // ==================== 工具方法 ====================

  /**
   * 延迟执行
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成随机测试数据
   */
  static generateTestData() {
    return {
      ...TEST_DATA,
      timestamp: Date.now(),
      randomId: Math.random().toString(36).substring(7)
    };
  }
}

// 在开发环境下自动暴露到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).WebViewTester = WebViewTester;
  
  // 自动启用模拟（如果需要的话）
  setTimeout(() => {
    if (!NativeBridge.isInWebView()) {
      console.log('🔧 检测到浏览器环境，自动启用模拟桥接');
      WebViewTester.mockNativeBridge();
    }
  }, 1000);

  // 添加快捷命令到控制台
  console.log(`
🧪 WebView测试工具已加载！

快捷命令：
- WebViewTester.runAllTests()           // 运行所有自动测试
- WebViewTester.runInteractiveTests()   // 运行交互测试
- WebViewTester.showBridgeInfo()        // 显示调试信息
- WebViewTester.mockNativeBridge()      // 启用模拟环境
- WebViewTester.generateTestReport()    // 生成测试报告
- WebViewTester.cleanup()               // 清理测试环境

单项测试：
- WebViewTester.testWechatLogin()       // 微信登录
- WebViewTester.testWechatPay()         // 微信支付
- WebViewTester.testPermissionCheck()   // 权限检查
- WebViewTester.testImageDownload()     // 图片下载
  `);
}

export default WebViewTester; 