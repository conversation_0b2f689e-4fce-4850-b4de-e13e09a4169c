// 原生App与H5页面通信桥梁 - 基于WebViewActivity.java实际接口
// Native App and H5 communication bridge - Based on actual WebViewActivity.java interfaces

// 权限类型常量
export const PERMISSION_TYPES = {
  CAMERA: 'camera',
  GALLERY_PHOTO: 'gallery_photo',
  GALLERY_VIDEO: 'gallery_video',
  GALLERY_AUDIO: 'gallery_audio',
  STORAGE: 'storage',
  AUDIO: 'audio',
  NOTICE: 'notice',
  CONTACT: 'contact'
} as const;

// 支付状态
export const PAY_STATUS = {
  SUCCESS: '1',    // 支付成功
  FAILED: '2',     // 支付失败
  CANCELLED: '3',  // 用户取消
  PENDING: '4'     // 等待确认
} as const;

// 权限信息接口
export interface PermissionInfo {
  openCamera: boolean;        // 相机权限
  openGalleryPhoto: boolean;  // 相册图片权限
  openGalleryVideo: boolean;  // 相册视频权限
  openGalleryAudio: boolean;  // 相册音频权限
  openStorage: boolean;       // 存储权限
  openContacts: boolean;      // 通讯录权限
  openLocation: boolean;      // 位置权限
  openAudio: boolean;         // 麦克风权限
  openNotice: boolean;        // 通知权限
}

// 微信登录用户信息
export interface WxUserInfo {
  nickname: string;
  head_img: string;
  unionid: string;
}

// Android WebView注入的接口 - 基于WebViewActivity.java实际注入的接口
declare global {
  interface Window {
    // 应用控制类
    exitApp?: {
      exitApp: () => void;
    };
    isOtherApp?: {
      isOtherApp: () => void;
    };
    acceptPrivateAgreement?: {
      acceptPrivateAgreement: () => void;
    };

    // 微信相关功能
    openWx?: {
      openWx: () => void;
    };
    wxLogin?: {
      wxLogin: () => void;
    };
    wxPay?: {
      wxPay: (payInfo: string) => void;
    };
    shareImgWx?: {
      shareImgWx: (url: string) => void;
    };
    shareImgWxFavorite?: {
      shareImgWxFavorite: (url: string) => void;
    };
    shareBase64ImgWx?: {
      shareBase64ImgWx: (base64Img: string) => void;
    };
    shareBase64ImgWxCircle?: {
      shareBase64ImgWxCircle: (base64Img: string) => void;
    };

    // 文件下载和处理
    downloadImg?: {
      downloadImg: (url: string) => void;
    };
    downloadBase64Img?: {
      downloadBase64Img: (base64Img: string) => void;
    };
    downloadVideo?: {
      downloadVideo: (url: string) => void;
    };
    downloadFile?: {
      downloadFile: (url: string) => void;
    };

    // 支付功能
    aliPay?: {
      aliPay: (payInfo: string) => void;
    };

    // 权限管理
    checkPermission?: {
      checkPermission: () => void;
    };
    requestPermission?: {
      requestPermission: (type: string) => void;
    };
    openSetting?: {
      openSetting: () => void;
    };

    // UI控制
    StatusBarDarkMode?: {
      StatusBarDarkMode: () => void;
    };
    StatusBarLightMode?: {
      StatusBarLightMode: () => void;
    };
    setPhotoNum?: {
      setPhotoNum: (num: number) => void;
    };

    // 小程序功能
    openMiniProgramTrend?: {
      openMiniProgramTrend: (dynamicId: string) => void;
    };
    openMiniProgramUser?: {
      openMiniProgramUser: (userId: string) => void;
    };

    // 原生向H5的回调函数
    webPaySuc?: (status: string) => void;
    webDownloadSuc?: () => void;
    webDownloadFail?: () => void;
    webPermissonConsent?: () => void;
    webPermissonDeny?: () => void;
    checkPermissionCallBack?: (permissionInfo: PermissionInfo) => void;
    wxLoginInfo?: (userInfo: WxUserInfo) => void;
    wxLoginCode?: (code: string) => void;
    webShowKeyBoard?: () => void;
    webHideKeyBoard?: () => void;
    noNetWork?: () => void;
    goToH5Page?: (type: number) => void;

    // iOS WebView (保留原有结构)
    webkit?: {
      messageHandlers?: {
        [key: string]: {
          postMessage: (params: any) => void;
        };
      };
    };
  }
}

// 原生桥接管理类 - 基于WebViewActivity.java实际接口
export class NativeBridge {

  // 检测是否在WebView环境中
  static isInWebView(): boolean {
    return !!(
      window.exitApp ||
      window.wxLogin ||
      window.downloadImg ||
      window.webkit?.messageHandlers
    );
  }

  // 检测是否为Android WebView
  static isAndroidWebView(): boolean {
    return !!(window.exitApp || window.wxLogin || window.downloadImg);
  }

  // 检测是否为iOS WebView
  static isIOSWebView(): boolean {
    return !!window.webkit?.messageHandlers;
  }

  // ==================== 应用控制类 ====================

  // 退出应用
  static exitApp(): void {
    try {
      window.exitApp?.exitApp();
    } catch (error) {
      console.error('退出应用失败:', error);
    }
  }

  // 检测是否为其他应用
  static isOtherApp(): void {
    try {
      window.isOtherApp?.isOtherApp();
    } catch (error) {
      console.error('检测其他应用失败:', error);
    }
  }

  // 接受隐私协议
  static acceptPrivateAgreement(): void {
    try {
      window.acceptPrivateAgreement?.acceptPrivateAgreement();
    } catch (error) {
      console.error('接受隐私协议失败:', error);
    }
  }

  // ==================== 微信相关功能 ====================

  // 打开微信
  static openWechat(): void {
    try {
      window.openWx?.openWx();
    } catch (error) {
      console.error('打开微信失败:', error);
    }
  }

  // 微信登录
  static wxLogin(): Promise<WxUserInfo> {
    return new Promise((resolve, reject) => {
      try {
        // 设置回调监听
        const originalCallback = window.wxLoginInfo;
        window.wxLoginInfo = (userInfo: WxUserInfo) => {
          // 恢复原回调
          window.wxLoginInfo = originalCallback;

          if (userInfo.unionid) {
            resolve(userInfo);
          } else {
            reject(new Error('微信登录取消或失败'));
          }
        };

        // 调用原生登录
        window.wxLogin?.wxLogin();

        // 设置超时
        setTimeout(() => {
          window.wxLoginInfo = originalCallback;
          reject(new Error('微信登录超时'));
        }, 30000);
      } catch (error) {
        console.error('微信登录失败:', error);
        reject(error);
      }
    });
  }

  // 微信支付
  static wxPay(payInfo: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // 设置支付回调监听
        const originalCallback = window.webPaySuc;
        window.webPaySuc = (status: string) => {
          // 恢复原回调
          window.webPaySuc = originalCallback;
          resolve(status);
        };

        // 调用原生支付
        window.wxPay?.wxPay(payInfo);

        // 设置超时
        setTimeout(() => {
          window.webPaySuc = originalCallback;
          reject(new Error('微信支付超时'));
        }, 60000);
      } catch (error) {
        console.error('微信支付失败:', error);
        reject(error);
      }
    });
  }

  // 分享图片到微信好友
  static shareImageToWechat(imageUrl: string): void {
    try {
      window.shareImgWx?.shareImgWx(imageUrl);
    } catch (error) {
      console.error('分享图片到微信好友失败:', error);
    }
  }

  // 分享图片到微信收藏
  static shareImageToWechatFavorite(imageUrl: string): void {
    try {
      window.shareImgWxFavorite?.shareImgWxFavorite(imageUrl);
    } catch (error) {
      console.error('分享图片到微信收藏失败:', error);
    }
  }

  // 分享Base64图片到微信好友
  static shareBase64ImageToWechat(base64Image: string): void {
    try {
      window.shareBase64ImgWx?.shareBase64ImgWx(base64Image);
    } catch (error) {
      console.error('分享Base64图片到微信好友失败:', error);
    }
  }

  // 分享Base64图片到朋友圈
  static shareBase64ImageToWechatCircle(base64Image: string): void {
    try {
      window.shareBase64ImgWxCircle?.shareBase64ImgWxCircle(base64Image);
    } catch (error) {
      console.error('分享Base64图片到朋友圈失败:', error);
    }
  }

  // ==================== 支付功能 ====================

  // 支付宝支付
  static aliPay(payInfo: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // 设置支付回调监听
        const originalCallback = window.webPaySuc;
        window.webPaySuc = (status: string) => {
          // 恢复原回调
          window.webPaySuc = originalCallback;
          resolve(status);
        };

        // 调用原生支付
        window.aliPay?.aliPay(payInfo);

        // 设置超时
        setTimeout(() => {
          window.webPaySuc = originalCallback;
          reject(new Error('支付宝支付超时'));
        }, 60000);
      } catch (error) {
        console.error('支付宝支付失败:', error);
        reject(error);
      }
    });
  }

  // ==================== 文件下载和处理 ====================

  // 下载图片
  static downloadImage(imageUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 设置下载回调监听
        const originalSuccessCallback = window.webDownloadSuc;
        const originalFailCallback = window.webDownloadFail;

        window.webDownloadSuc = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          resolve();
        };

        window.webDownloadFail = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('图片下载失败'));
        };

        // 调用原生下载
        window.downloadImg?.downloadImg(imageUrl);

        // 设置超时
        setTimeout(() => {
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('图片下载超时'));
        }, 30000);
      } catch (error) {
        console.error('下载图片失败:', error);
        reject(error);
      }
    });
  }

  // 下载Base64图片
  static downloadBase64Image(base64Image: string): void {
    try {
      window.downloadBase64Img?.downloadBase64Img(base64Image);
    } catch (error) {
      console.error('下载Base64图片失败:', error);
    }
  }

  // 保存图片到相册（通过原生方法）
  static async saveImageToAlbumNative(imageData: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        if (!this.isInWebView()) {
          reject(new Error('保存图片功能仅在WebView环境中可用'));
          return;
        }

        if (!window.downloadBase64Img) {
          reject(new Error('原生保存接口不可用，请检查应用版本'));
          return;
        }

        // 处理 Base64 数据格式
        let processedData = imageData;
        if (imageData.startsWith('data:image/')) {
          // 移除 data:image/png;base64, 前缀
          processedData = imageData.split(',')[1];
        }

        // 验证 Base64 数据
        if (!processedData || processedData.length < 100) {
          reject(new Error('图片数据无效或过小'));
          return;
        }

        // 检查数据大小（防止内存溢出）
        const dataSizeKB = (processedData.length * 3) / 4 / 1024;
        if (dataSizeKB > 10240) { // 超过10MB
          reject(new Error('图片过大，无法保存'));
          return;
        }

        // 设置超时处理
        const timeoutId = setTimeout(() => {
          cleanup();
          reject(new Error('保存操作超时，请检查网络或重试'));
        }, 30000); // 30秒超时

        // 保存原始回调
        const originalSuccessCallback = window.webDownloadSuc;
        const originalFailCallback = window.webDownloadFail;

        const cleanup = () => {
          clearTimeout(timeoutId);
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
        };

        // 设置成功回调
        window.webDownloadSuc = () => {
          cleanup();
          resolve(true);
        };

        // 设置失败回调
        window.webDownloadFail = () => {
          cleanup();
          reject(new Error('保存失败，可能是权限不足或存储空间不够'));
        };

        // 调用原生方法保存图片
        window.downloadBase64Img.downloadBase64Img(processedData);
        
        console.log('保存图片请求已发送，等待原生响应...');
        
      } catch (error) {
        console.error('保存图片到相册失败:', error);
        reject(new Error(`保存操作异常: ${error.message}`));
      }
    });
  }

  // 下载视频
  static downloadVideo(videoUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 设置下载回调监听
        const originalSuccessCallback = window.webDownloadSuc;
        const originalFailCallback = window.webDownloadFail;

        window.webDownloadSuc = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          resolve();
        };

        window.webDownloadFail = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('视频下载失败'));
        };

        // 调用原生下载
        window.downloadVideo?.downloadVideo(videoUrl);

        // 设置超时
        setTimeout(() => {
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('视频下载超时'));
        }, 60000);
      } catch (error) {
        console.error('下载视频失败:', error);
        reject(error);
      }
    });
  }

  // 下载文件
  static downloadFile(fileUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 设置下载回调监听
        const originalSuccessCallback = window.webDownloadSuc;
        const originalFailCallback = window.webDownloadFail;

        window.webDownloadSuc = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          resolve();
        };

        window.webDownloadFail = () => {
          // 恢复原回调
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('文件下载失败'));
        };

        // 调用原生下载
        window.downloadFile?.downloadFile(fileUrl);

        // 设置超时
        setTimeout(() => {
          window.webDownloadSuc = originalSuccessCallback;
          window.webDownloadFail = originalFailCallback;
          reject(new Error('文件下载超时'));
        }, 60000);
      } catch (error) {
        console.error('下载文件失败:', error);
        reject(error);
      }
    });
  }

  // ==================== 权限管理 ====================

  // 检查权限
  static checkPermission(): Promise<PermissionInfo> {
    return new Promise((resolve, reject) => {
      try {
        // 设置权限检查回调监听
        const originalCallback = window.checkPermissionCallBack;
        window.checkPermissionCallBack = (permissionInfo: PermissionInfo) => {
          // 恢复原回调
          window.checkPermissionCallBack = originalCallback;
          resolve(permissionInfo);
        };

        // 调用原生权限检查
        window.checkPermission?.checkPermission();

        // 设置超时
        setTimeout(() => {
          window.checkPermissionCallBack = originalCallback;
          reject(new Error('权限检查超时'));
        }, 10000);
      } catch (error) {
        console.error('检查权限失败:', error);
        reject(error);
      }
    });
  }

  // 请求权限
  static requestPermission(permissionType: keyof typeof PERMISSION_TYPES): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        // 设置权限请求回调监听
        const originalConsentCallback = window.webPermissonConsent;
        const originalDenyCallback = window.webPermissonDeny;

        window.webPermissonConsent = () => {
          // 恢复原回调
          window.webPermissonConsent = originalConsentCallback;
          window.webPermissonDeny = originalDenyCallback;
          resolve(true);
        };

        window.webPermissonDeny = () => {
          // 恢复原回调
          window.webPermissonConsent = originalConsentCallback;
          window.webPermissonDeny = originalDenyCallback;
          resolve(false);
        };

        // 调用原生权限请求
        window.requestPermission?.requestPermission(PERMISSION_TYPES[permissionType]);

        // 设置超时
        setTimeout(() => {
          window.webPermissonConsent = originalConsentCallback;
          window.webPermissonDeny = originalDenyCallback;
          reject(new Error('权限请求超时'));
        }, 30000);
      } catch (error) {
        console.error('请求权限失败:', error);
        reject(error);
      }
    });
  }

  // 打开系统设置
  static openSetting(): void {
    try {
      window.openSetting?.openSetting();
    } catch (error) {
      console.error('打开系统设置失败:', error);
    }
  }

  // ==================== UI控制 ====================

  // 设置状态栏深色模式（黑字）
  static setStatusBarDarkMode(): void {
    try {
      window.StatusBarDarkMode?.StatusBarDarkMode();
    } catch (error) {
      console.error('设置状态栏深色模式失败:', error);
    }
  }

  // 设置状态栏浅色模式（白字）
  static setStatusBarLightMode(): void {
    try {
      window.StatusBarLightMode?.StatusBarLightMode();
    } catch (error) {
      console.error('设置状态栏浅色模式失败:', error);
    }
  }

  // 设置照片选择数量
  static setPhotoSelectNum(num: number): void {
    try {
      window.setPhotoNum?.setPhotoNum(num);
    } catch (error) {
      console.error('设置照片选择数量失败:', error);
    }
  }

  // ==================== 小程序功能 ====================

  // 打开小程序动态页面
  static openMiniProgramTrend(dynamicId: string): void {
    try {
      window.openMiniProgramTrend?.openMiniProgramTrend(dynamicId);
    } catch (error) {
      console.error('打开小程序动态页面失败:', error);
    }
  }

  // 打开小程序用户页面
  static openMiniProgramUser(userId: string): void {
    try {
      window.openMiniProgramUser?.openMiniProgramUser(userId);
    } catch (error) {
      console.error('打开小程序用户页面失败:', error);
    }
  }

  // ==================== 调试和工具方法 ====================

  // 获取调试信息
  static getDebugInfo() {
    return {
      environment: {
        isInWebView: this.isInWebView(),
        isAndroidWebView: this.isAndroidWebView(),
        isIOSWebView: this.isIOSWebView(),
        userAgent: navigator.userAgent
      },
      availableInterfaces: {
        // 应用控制类
        exitApp: !!window.exitApp,
        isOtherApp: !!window.isOtherApp,
        acceptPrivateAgreement: !!window.acceptPrivateAgreement,

        // 微信相关
        openWx: !!window.openWx,
        wxLogin: !!window.wxLogin,
        wxPay: !!window.wxPay,
        shareImgWx: !!window.shareImgWx,
        shareImgWxFavorite: !!window.shareImgWxFavorite,
        shareBase64ImgWx: !!window.shareBase64ImgWx,
        shareBase64ImgWxCircle: !!window.shareBase64ImgWxCircle,

        // 文件下载
        downloadImg: !!window.downloadImg,
        downloadBase64Img: !!window.downloadBase64Img,
        downloadVideo: !!window.downloadVideo,
        downloadFile: !!window.downloadFile,

        // 支付
        aliPay: !!window.aliPay,

        // 权限管理
        checkPermission: !!window.checkPermission,
        requestPermission: !!window.requestPermission,
        openSetting: !!window.openSetting,

        // UI控制
        StatusBarDarkMode: !!window.StatusBarDarkMode,
        StatusBarLightMode: !!window.StatusBarLightMode,
        setPhotoNum: !!window.setPhotoNum,

        // 小程序
        openMiniProgramTrend: !!window.openMiniProgramTrend,
        openMiniProgramUser: !!window.openMiniProgramUser
      },
      localStorage: {
        ddxc_version: localStorage.getItem('ddxc_version'),
        mobile_model: localStorage.getItem('mobile_model'),
        phone_model: localStorage.getItem('phone_model')
      }
    };
  }
}

// 导出默认实例
export default NativeBridge;